import json
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404
from django.db.models import Q
from django.core.paginator import Paginator
from .models import Course, Category, Lesson, Material, Enrollment, QuestionAnswer, LessonProgress
from users.models import User


@require_http_methods(["GET"])
def courses_list_api(request):
    """Simple API endpoint for course listing with search, filter, and pagination"""
    courses = Course.objects.filter(is_active=True).select_related('instructor_id', 'category_id')
    categories = Category.objects.filter(is_active=True)
    
    # Search functionality
    search = request.GET.get('search')
    if search:
        courses = courses.filter(
            Q(title__icontains=search) |
            Q(description__icontains=search) |
            Q(instructor_id__first_name__icontains=search) |
            Q(instructor_id__last_name__icontains=search)
        )
    
    # Category filter
    category_filter = request.GET.get('category')
    if category_filter:
        courses = courses.filter(category_id__id=category_filter)
    
    # Enrolled filter for students
    enrolled_filter = request.GET.get('enrolled')
    if enrolled_filter and request.user.is_authenticated:
        if request.user.role == 'student':
            enrolled_course_ids = Enrollment.objects.filter(
                student_id=request.user, is_active=True
            ).values_list('course_id', flat=True)
            courses = courses.filter(id__in=enrolled_course_ids)
    
    # Pagination
    page = request.GET.get('page', 1)
    paginator = Paginator(courses, 12)
    courses_page = paginator.get_page(page)
    
    # Convert courses to dict
    courses_data = []
    for course in courses_page:
        courses_data.append({
            'id': course.id,
            'title': course.title,
            'description': course.description,
            'price': course.price,
            'duration': course.duration,
            'banner': course.banner.url if course.banner and hasattr(course.banner, 'url') else None,
            'category': {
                'id': course.category_id.id,
                'title': course.category_id.title,
            } if course.category_id else None,
            'instructor': {
                'id': course.instructor_id.id,
                'username': course.instructor_id.username,
                'first_name': course.instructor_id.first_name,
                'last_name': course.instructor_id.last_name,
            } if course.instructor_id else None,
        })
    
    # Convert categories to dict
    categories_data = []
    for category in categories:
        categories_data.append({
            'id': category.id,
            'title': category.title,
        })
    
    return JsonResponse({
        'courses': courses_data,
        'categories': categories_data,
        'pagination': {
            'current_page': courses_page.number,
            'total_pages': paginator.num_pages,
            'has_next': courses_page.has_next(),
            'has_previous': courses_page.has_previous(),
            'total_count': paginator.count
        },
        'search': search,
        'category_filter': category_filter
    })


@require_http_methods(["GET"])
def course_detail_api(request, course_id):
    """Simple API endpoint for course detail"""
    course = get_object_or_404(Course, id=course_id, is_active=True)
    
    # Check if user is enrolled
    is_enrolled = False
    enrollment = None
    if request.user.is_authenticated and request.user.role == 'student':
        try:
            enrollment = Enrollment.objects.get(student_id=request.user, course_id=course, is_active=True)
            is_enrolled = True
        except Enrollment.DoesNotExist:
            pass
    
    # Get lessons (only if enrolled or is instructor)
    lessons_data = []
    if is_enrolled or (request.user.is_authenticated and request.user == course.instructor_id):
        lessons = Lesson.objects.filter(course_id=course, is_active=True).order_by('order', 'created_at')
        for lesson in lessons:
            lessons_data.append({
                'id': lesson.id,
                'title': lesson.title,
                'description': lesson.description,
                'lesson_type': lesson.lesson_type,
                'duration_minutes': lesson.duration_minutes,
                'order': lesson.order,
            })
    
    # Get materials (only if enrolled or is instructor)
    materials_data = []
    if is_enrolled or (request.user.is_authenticated and request.user == course.instructor_id):
        materials = Material.objects.filter(course_id=course, is_active=True)
        for material in materials:
            materials_data.append({
                'id': material.id,
                'title': material.title,
                'description': material.description,
                'file_type': material.file_type,
                'file': material.file.url if material.file and hasattr(material.file, 'url') else None,
            })
    
    course_data = {
        'id': course.id,
        'title': course.title,
        'description': course.description,
        'price': course.price,
        'duration': course.duration,
        'banner': course.banner.url if course.banner and hasattr(course.banner, 'url') else None,
        'category': {
            'id': course.category_id.id,
            'title': course.category_id.title,
        } if course.category_id else None,
        'instructor': {
            'id': course.instructor_id.id,
            'username': course.instructor_id.username,
            'first_name': course.instructor_id.first_name,
            'last_name': course.instructor_id.last_name,
        } if course.instructor_id else None,
        'lessons': lessons_data,
        'materials': materials_data,
        'total_lessons': len(lessons_data),
        'total_students': Enrollment.objects.filter(course_id=course, is_active=True).count(),
    }
    
    enrollment_data = None
    if enrollment:
        enrollment_data = {
            'id': enrollment.id,
            'progress': enrollment.progress,
            'is_completed': enrollment.is_completed,
            'price': enrollment.price,
        }
    
    return JsonResponse({
        'course': course_data,
        'is_enrolled': is_enrolled,
        'enrollment': enrollment_data,
        'can_access_content': is_enrolled or (request.user.is_authenticated and request.user == course.instructor_id)
    })


@csrf_exempt
@login_required
@require_http_methods(["POST"])
def enroll_course_api(request, course_id):
    """Simple API endpoint for course enrollment"""
    if request.user.role != 'student':
        return JsonResponse({'error': 'Only students can enroll in courses'}, status=403)
    
    course = get_object_or_404(Course, id=course_id, is_active=True)
    
    # Check if already enrolled
    if Enrollment.objects.filter(student_id=request.user, course_id=course, is_active=True).exists():
        return JsonResponse({'error': 'Already enrolled in this course'}, status=400)
    
    # Create enrollment
    enrollment = Enrollment.objects.create(
        student_id=request.user,
        course_id=course,
        price=course.price,
        is_active=True
    )
    
    return JsonResponse({
        'message': 'Successfully enrolled in course!',
        'enrollment': {
            'id': enrollment.id,
            'progress': enrollment.progress,
            'is_completed': enrollment.is_completed,
            'price': enrollment.price,
        }
    })


@require_http_methods(["GET"])
def lesson_detail_api(request, course_id, lesson_id):
    """Simple API endpoint for lesson detail"""
    course = get_object_or_404(Course, id=course_id, is_active=True)
    lesson = get_object_or_404(Lesson, id=lesson_id, course_id=course, is_active=True)
    
    # Check access permissions
    can_access = False
    if request.user.is_authenticated:
        if request.user.role == 'student':
            can_access = Enrollment.objects.filter(
                student_id=request.user, course_id=course, is_active=True
            ).exists()
        elif request.user == course.instructor_id:
            can_access = True
    
    if not can_access:
        return JsonResponse({'error': 'Access denied'}, status=403)
    
    # Get lesson progress for student
    lesson_progress_data = None
    if request.user.role == 'student':
        lesson_progress, created = LessonProgress.objects.get_or_create(
            student=request.user,
            lesson=lesson,
            defaults={'is_completed': False, 'watch_time_seconds': 0}
        )
        lesson_progress_data = {
            'id': lesson_progress.id,
            'is_completed': lesson_progress.is_completed,
            'watch_time_seconds': lesson_progress.watch_time_seconds,
        }
    
    # Get all lessons for navigation
    all_lessons = Lesson.objects.filter(course_id=course, is_active=True).order_by('order', 'created_at')
    lesson_list = list(all_lessons)
    current_index = lesson_list.index(lesson)
    
    previous_lesson = lesson_list[current_index - 1] if current_index > 0 else None
    next_lesson = lesson_list[current_index + 1] if current_index < len(lesson_list) - 1 else None
    
    # Convert lesson data
    lesson_data = {
        'id': lesson.id,
        'title': lesson.title,
        'description': lesson.description,
        'lesson_type': lesson.lesson_type,
        'youtube_url': lesson.youtube_url,
        'video': lesson.video.url if lesson.video and hasattr(lesson.video, 'url') else None,
        'text_content': lesson.text_content,
        'duration_minutes': lesson.duration_minutes,
        'order': lesson.order,
    }
    
    course_data = {
        'id': course.id,
        'title': course.title,
        'description': course.description,
    }
    
    previous_lesson_data = None
    if previous_lesson:
        previous_lesson_data = {
            'id': previous_lesson.id,
            'title': previous_lesson.title,
        }
    
    next_lesson_data = None
    if next_lesson:
        next_lesson_data = {
            'id': next_lesson.id,
            'title': next_lesson.title,
        }
    
    # Convert all lessons for navigation
    all_lessons_data = []
    for l in all_lessons:
        all_lessons_data.append({
            'id': l.id,
            'title': l.title,
            'order': l.order,
        })
    
    return JsonResponse({
        'lesson': lesson_data,
        'course': course_data,
        'lesson_progress': lesson_progress_data,
        'previous_lesson': previous_lesson_data,
        'next_lesson': next_lesson_data,
        'all_lessons': all_lessons_data
    })
