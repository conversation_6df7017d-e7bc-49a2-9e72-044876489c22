#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create sample data for testing the React frontend
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'lms_backend.settings')
django.setup()

from users.models import User
from core.models import Category, Course, Lesson, Material, Enrollment

def create_sample_data():
    print("Creating sample data...")
    
    # Create categories
    categories = [
        {'title': 'Programming', 'is_active': True},
        {'title': 'Data Science', 'is_active': True},
        {'title': 'Web Development', 'is_active': True},
        {'title': 'Mobile Development', 'is_active': True},
        {'title': 'Machine Learning', 'is_active': True},
    ]
    
    for cat_data in categories:
        category, created = Category.objects.get_or_create(
            title=cat_data['title'],
            defaults=cat_data
        )
        if created:
            print(f"Created category: {category.title}")
    
    # Create sample users
    users_data = [
        {
            'username': 'teacher1',
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON>',
            'role': 'teacher',
            'password': 'teacher123'
        },
        {
            'username': 'teacher2',
            'email': '<EMAIL>',
            'first_name': 'Sarah',
            'last_name': '<PERSON>',
            'role': 'teacher',
            'password': 'teacher123'
        },
        {
            'username': 'student1',
            'email': '<EMAIL>',
            'first_name': 'Mike',
            'last_name': 'Davis',
            'role': 'student',
            'password': 'student123'
        },
        {
            'username': 'student2',
            'email': '<EMAIL>',
            'first_name': 'Emily',
            'last_name': 'Wilson',
            'role': 'student',
            'password': 'student123'
        },
    ]
    
    for user_data in users_data:
        if not User.objects.filter(username=user_data['username']).exists():
            password = user_data.pop('password')
            user = User.objects.create_user(password=password, **user_data)
            print(f"Created user: {user.username} ({user.role})")
    
    # Create sample courses
    programming_cat = Category.objects.get(title='Programming')
    web_dev_cat = Category.objects.get(title='Web Development')
    data_science_cat = Category.objects.get(title='Data Science')
    
    teacher1 = User.objects.get(username='teacher1')
    teacher2 = User.objects.get(username='teacher2')
    
    courses_data = [
        {
            'title': 'Python Programming for Beginners',
            'description': 'Learn Python programming from scratch. This comprehensive course covers all the basics of Python programming including variables, data types, control structures, functions, and object-oriented programming.',
            'price': 49.99,
            'duration': 40,
            'category_id': programming_cat,
            'instructor_id': teacher1,
            'is_active': True
        },
        {
            'title': 'React.js Complete Course',
            'description': 'Master React.js and build modern web applications. Learn components, state management, hooks, routing, and more in this hands-on course.',
            'price': 79.99,
            'duration': 60,
            'category_id': web_dev_cat,
            'instructor_id': teacher1,
            'is_active': True
        },
        {
            'title': 'Data Science with Python',
            'description': 'Dive into data science using Python. Learn pandas, numpy, matplotlib, and machine learning basics to analyze and visualize data.',
            'price': 99.99,
            'duration': 80,
            'category_id': data_science_cat,
            'instructor_id': teacher2,
            'is_active': True
        },
        {
            'title': 'JavaScript Fundamentals',
            'description': 'Master JavaScript from the ground up. Learn ES6+, DOM manipulation, async programming, and modern JavaScript development.',
            'price': 0,  # Free course
            'duration': 30,
            'category_id': web_dev_cat,
            'instructor_id': teacher2,
            'is_active': True
        },
        {
            'title': 'Advanced Python Programming',
            'description': 'Take your Python skills to the next level. Learn advanced concepts like decorators, generators, context managers, and more.',
            'price': 69.99,
            'duration': 50,
            'category_id': programming_cat,
            'instructor_id': teacher1,
            'is_active': True
        },
    ]
    
    for course_data in courses_data:
        course, created = Course.objects.get_or_create(
            title=course_data['title'],
            defaults=course_data
        )
        if created:
            print(f"Created course: {course.title}")
            
            # Create sample lessons for each course
            lessons_data = [
                {
                    'title': f'Introduction to {course.title}',
                    'description': f'Welcome to {course.title}. In this lesson, we will cover the basics and what you will learn.',
                    'lesson_type': 'video',
                    'text_content': f'Welcome to {course.title}! This is an exciting journey ahead.',
                    'duration_minutes': 15,
                    'order': 1,
                    'course_id': course,
                    'is_active': True
                },
                {
                    'title': f'Getting Started with {course.title.split()[0]}',
                    'description': f'Learn the fundamentals and setup required for {course.title}.',
                    'lesson_type': 'video',
                    'text_content': f'In this lesson, we will set up our development environment.',
                    'duration_minutes': 25,
                    'order': 2,
                    'course_id': course,
                    'is_active': True
                },
                {
                    'title': f'Hands-on Practice',
                    'description': f'Practice what you have learned in {course.title} with real examples.',
                    'lesson_type': 'text',
                    'text_content': f'Now it\'s time to practice! Here are some exercises to reinforce your learning.',
                    'duration_minutes': 30,
                    'order': 3,
                    'course_id': course,
                    'is_active': True
                },
            ]
            
            for lesson_data in lessons_data:
                lesson = Lesson.objects.create(**lesson_data)
                print(f"  Created lesson: {lesson.title}")
    
    # Create sample enrollments
    student1 = User.objects.get(username='student1')
    student2 = User.objects.get(username='student2')
    
    python_course = Course.objects.get(title='Python Programming for Beginners')
    react_course = Course.objects.get(title='React.js Complete Course')
    js_course = Course.objects.get(title='JavaScript Fundamentals')
    
    enrollments_data = [
        {
            'student_id': student1,
            'course_id': python_course,
            'price': python_course.price,
            'is_active': True,
            'progress': 25
        },
        {
            'student_id': student1,
            'course_id': js_course,
            'price': js_course.price,
            'is_active': True,
            'progress': 60
        },
        {
            'student_id': student2,
            'course_id': react_course,
            'price': react_course.price,
            'is_active': True,
            'progress': 10
        },
    ]
    
    for enrollment_data in enrollments_data:
        enrollment, created = Enrollment.objects.get_or_create(
            student_id=enrollment_data['student_id'],
            course_id=enrollment_data['course_id'],
            defaults=enrollment_data
        )
        if created:
            print(f"Created enrollment: {enrollment.student_id.username} -> {enrollment.course_id.title}")
    
    print("\nSample data created successfully!")
    print("\nTest accounts:")
    print("- Admin: admin / admin123")
    print("- Teacher: teacher1 / teacher123")
    print("- Teacher: teacher2 / teacher123") 
    print("- Student: student1 / student123")
    print("- Student: student2 / student123")

if __name__ == "__main__":
    create_sample_data()
