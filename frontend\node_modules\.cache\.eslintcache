[{"E:\\Downloads\\lms_backend\\frontend\\src\\index.js": "1", "E:\\Downloads\\lms_backend\\frontend\\src\\App.js": "2", "E:\\Downloads\\lms_backend\\frontend\\src\\contexts\\AuthContext.js": "3", "E:\\Downloads\\lms_backend\\frontend\\src\\pages\\Home.js": "4", "E:\\Downloads\\lms_backend\\frontend\\src\\components\\common\\ProtectedRoute.js": "5", "E:\\Downloads\\lms_backend\\frontend\\src\\components\\layout\\Layout.js": "6", "E:\\Downloads\\lms_backend\\frontend\\src\\components\\auth\\Login.js": "7", "E:\\Downloads\\lms_backend\\frontend\\src\\components\\dashboard\\Dashboard.js": "8", "E:\\Downloads\\lms_backend\\frontend\\src\\components\\auth\\Signup.js": "9", "E:\\Downloads\\lms_backend\\frontend\\src\\components\\courses\\CourseList.js": "10", "E:\\Downloads\\lms_backend\\frontend\\src\\components\\layout\\Navbar.js": "11", "E:\\Downloads\\lms_backend\\frontend\\src\\components\\layout\\Footer.js": "12", "E:\\Downloads\\lms_backend\\frontend\\src\\services\\api.js": "13", "E:\\Downloads\\lms_backend\\frontend\\src\\components\\courses\\CourseDetail.js": "14"}, {"size": 254, "mtime": 1754852398055, "results": "15", "hashOfConfig": "16"}, {"size": 1785, "mtime": 1754855445902, "results": "17", "hashOfConfig": "16"}, {"size": 2558, "mtime": 1754852456748, "results": "18", "hashOfConfig": "16"}, {"size": 13456, "mtime": 1754855582533, "results": "19", "hashOfConfig": "16"}, {"size": 1375, "mtime": 1754852744791, "results": "20", "hashOfConfig": "16"}, {"size": 1037, "mtime": 1754852509703, "results": "21", "hashOfConfig": "16"}, {"size": 4803, "mtime": 1754852588488, "results": "22", "hashOfConfig": "16"}, {"size": 12493, "mtime": 1754855696419, "results": "23", "hashOfConfig": "16"}, {"size": 10095, "mtime": 1754852619783, "results": "24", "hashOfConfig": "16"}, {"size": 10740, "mtime": 1754855614862, "results": "25", "hashOfConfig": "16"}, {"size": 5970, "mtime": 1754852481396, "results": "26", "hashOfConfig": "16"}, {"size": 3476, "mtime": 1754852499611, "results": "27", "hashOfConfig": "16"}, {"size": 2221, "mtime": 1754854320479, "results": "28", "hashOfConfig": "16"}, {"size": 15463, "mtime": 1754855647662, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1hz40je", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\Downloads\\lms_backend\\frontend\\src\\index.js", [], [], "E:\\Downloads\\lms_backend\\frontend\\src\\App.js", [], [], "E:\\Downloads\\lms_backend\\frontend\\src\\contexts\\AuthContext.js", [], [], "E:\\Downloads\\lms_backend\\frontend\\src\\pages\\Home.js", ["72"], [], "E:\\Downloads\\lms_backend\\frontend\\src\\components\\common\\ProtectedRoute.js", [], [], "E:\\Downloads\\lms_backend\\frontend\\src\\components\\layout\\Layout.js", [], [], "E:\\Downloads\\lms_backend\\frontend\\src\\components\\auth\\Login.js", ["73"], [], "E:\\Downloads\\lms_backend\\frontend\\src\\components\\dashboard\\Dashboard.js", [], [], "E:\\Downloads\\lms_backend\\frontend\\src\\components\\auth\\Signup.js", [], [], "E:\\Downloads\\lms_backend\\frontend\\src\\components\\courses\\CourseList.js", ["74", "75", "76"], [], "E:\\Downloads\\lms_backend\\frontend\\src\\components\\layout\\Navbar.js", ["77", "78", "79", "80"], [], "E:\\Downloads\\lms_backend\\frontend\\src\\components\\layout\\Footer.js", ["81", "82", "83", "84", "85", "86", "87", "88", "89", "90", "91", "92"], [], "E:\\Downloads\\lms_backend\\frontend\\src\\services\\api.js", [], [], "E:\\Downloads\\lms_backend\\frontend\\src\\components\\courses\\CourseDetail.js", ["93"], [], {"ruleId": "94", "severity": 1, "message": "95", "line": 239, "column": 19, "nodeType": "96", "endLine": 239, "endColumn": 72}, {"ruleId": "94", "severity": 1, "message": "95", "line": 113, "column": 19, "nodeType": "96", "endLine": 113, "endColumn": 81}, {"ruleId": "97", "severity": 1, "message": "98", "line": 7, "column": 11, "nodeType": "99", "messageId": "100", "endLine": 7, "endColumn": 15}, {"ruleId": "97", "severity": 1, "message": "101", "line": 7, "column": 17, "nodeType": "99", "messageId": "100", "endLine": 7, "endColumn": 32}, {"ruleId": "102", "severity": 1, "message": "103", "line": 18, "column": 6, "nodeType": "104", "endLine": 18, "endColumn": 20, "suggestions": "105"}, {"ruleId": "94", "severity": 1, "message": "95", "line": 77, "column": 15, "nodeType": "96", "endLine": 77, "endColumn": 48}, {"ruleId": "94", "severity": 1, "message": "95", "line": 80, "column": 15, "nodeType": "96", "endLine": 80, "endColumn": 48}, {"ruleId": "94", "severity": 1, "message": "95", "line": 83, "column": 15, "nodeType": "96", "endLine": 83, "endColumn": 48}, {"ruleId": "94", "severity": 1, "message": "95", "line": 105, "column": 17, "nodeType": "96", "endLine": 111, "endColumn": 18}, {"ruleId": "94", "severity": 1, "message": "95", "line": 13, "column": 15, "nodeType": "96", "endLine": 13, "endColumn": 44}, {"ruleId": "94", "severity": 1, "message": "95", "line": 14, "column": 15, "nodeType": "96", "endLine": 14, "endColumn": 44}, {"ruleId": "94", "severity": 1, "message": "95", "line": 15, "column": 15, "nodeType": "96", "endLine": 15, "endColumn": 44}, {"ruleId": "94", "severity": 1, "message": "95", "line": 16, "column": 15, "nodeType": "96", "endLine": 16, "endColumn": 44}, {"ruleId": "94", "severity": 1, "message": "95", "line": 17, "column": 15, "nodeType": "96", "endLine": 17, "endColumn": 27}, {"ruleId": "94", "severity": 1, "message": "95", "line": 35, "column": 36, "nodeType": "96", "endLine": 35, "endColumn": 48}, {"ruleId": "94", "severity": 1, "message": "95", "line": 36, "column": 36, "nodeType": "96", "endLine": 36, "endColumn": 48}, {"ruleId": "94", "severity": 1, "message": "95", "line": 37, "column": 36, "nodeType": "96", "endLine": 37, "endColumn": 48}, {"ruleId": "94", "severity": 1, "message": "95", "line": 39, "column": 36, "nodeType": "96", "endLine": 39, "endColumn": 48}, {"ruleId": "94", "severity": 1, "message": "95", "line": 68, "column": 13, "nodeType": "96", "endLine": 68, "endColumn": 53}, {"ruleId": "94", "severity": 1, "message": "95", "line": 69, "column": 13, "nodeType": "96", "endLine": 69, "endColumn": 53}, {"ruleId": "94", "severity": 1, "message": "95", "line": 70, "column": 13, "nodeType": "96", "endLine": 70, "endColumn": 48}, {"ruleId": "102", "severity": 1, "message": "106", "line": 17, "column": 6, "nodeType": "104", "endLine": 17, "endColumn": 16, "suggestions": "107"}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "'isAuthenticated' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCourses'. Either include it or remove the dependency array.", "ArrayExpression", ["108"], "React Hook useEffect has a missing dependency: 'loadCourseDetail'. Either include it or remove the dependency array.", ["109"], {"desc": "110", "fix": "111"}, {"desc": "112", "fix": "113"}, "Update the dependencies array to be: [loadCourses, searchParams]", {"range": "114", "text": "115"}, "Update the dependencies array to be: [courseId, loadCourseDetail]", {"range": "116", "text": "117"}, [771, 785], "[loadCourses, searchParams]", [615, 625], "[courseId, loadCourseDetail]"]