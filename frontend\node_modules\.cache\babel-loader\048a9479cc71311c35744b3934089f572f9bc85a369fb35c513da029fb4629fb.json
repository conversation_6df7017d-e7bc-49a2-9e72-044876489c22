{"ast": null, "code": "var _jsxFileName = \"E:\\\\Downloads\\\\lms_backend\\\\frontend\\\\src\\\\pages\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { homeAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const navigate = useNavigate();\n  const [homeData, setHomeData] = useState({\n    featured_courses: [],\n    categories: [],\n    total_courses: 0,\n    total_students: 0,\n    total_teachers: 0\n  });\n  const [searchQuery, setSearchQuery] = useState('');\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    loadHomeData();\n  }, []);\n  const loadHomeData = async () => {\n    try {\n      const response = await homeAPI.getHomeData();\n      setHomeData(response.data);\n    } catch (error) {\n      console.error('Failed to load home data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSearch = e => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      navigate(`/courses?search=${encodeURIComponent(searchQuery.trim())}`);\n    }\n  };\n  const formatPrice = price => {\n    return price === 0 ? 'Free' : `$${price}`;\n  };\n  const getUserAvatar = (user, size = 36) => {\n    if (!user) return null;\n    const initials = user.first_name && user.last_name ? `${user.first_name[0]}${user.last_name[0]}`.toUpperCase() : user.username[0].toUpperCase();\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-avatar d-flex align-items-center justify-content-center text-white rounded-circle\",\n      style: {\n        width: size,\n        height: size,\n        fontSize: size * 0.4\n      },\n      children: initials\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this);\n  };\n  const truncateWords = (text, wordLimit) => {\n    if (!text) return '';\n    const words = text.split(' ');\n    if (words.length <= wordLimit) return text;\n    return words.slice(0, wordLimit).join(' ') + '...';\n  };\n  const getCourseImage = course => {\n    var _course$category;\n    if (course.banner) {\n      return course.banner;\n    }\n\n    // Fallback images based on category\n    const categoryImages = {\n      'Programming': 'https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&h=240&q=80',\n      'Web Development': 'https://images.unsplash.com/photo-1547658719-da2b51169166?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&h=240&q=80',\n      'Data Science': 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&h=240&q=80',\n      'Mobile Development': 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&h=240&q=80',\n      'Machine Learning': 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&h=240&q=80'\n    };\n    return categoryImages[(_course$category = course.category) === null || _course$category === void 0 ? void 0 : _course$category.title] || 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&h=240&q=80';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        height: '50vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              children: \"Learn Without Limits\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"lead mb-4\",\n              children: \"Discover, learn, and grow with Pathshala. Join thousands of students and teachers in our interactive learning platform.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              className: \"search-form mb-4\",\n              onSubmit: handleSearch,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  className: \"form-control form-control-lg\",\n                  placeholder: \"What do you want to learn today?\",\n                  value: searchQuery,\n                  onChange: e => setSearchQuery(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-primary\",\n                  type: \"submit\",\n                  children: \"Search\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex flex-wrap\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/courses\",\n                className: \"btn btn-light btn-lg me-3 mb-3\",\n                children: \"Explore Courses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/signup?role=teacher\",\n                className: \"btn btn-outline-light btn-lg mb-3\",\n                children: \"Become a Teacher\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-6\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1350&q=80\",\n              alt: \"Students learning online\",\n              className: \"img-fluid rounded shadow-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-center mb-4\",\n          children: \"Browse Categories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex flex-wrap justify-content-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/courses\",\n            className: \"category-pill bg-light active text-decoration-none\",\n            children: \"All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), homeData.categories.map(category => /*#__PURE__*/_jsxDEV(Link, {\n            to: `/courses?category=${category.id}`,\n            className: \"category-pill bg-light text-decoration-none\",\n            children: category.title\n          }, category.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-5 bg-light\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Featured Courses\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/courses\",\n            className: \"btn btn-outline-primary\",\n            children: \"View All Courses\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row g-4\",\n          children: homeData.featured_courses.slice(0, 6).map(course => {\n            var _course$category2, _course$instructor, _course$instructor2, _course$instructor3;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6 col-lg-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card course-card h-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: course.banner || \"https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&h=240&q=80\",\n                  className: \"card-img-top\",\n                  alt: course.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-body\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-between align-items-center mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge badge-category\",\n                      children: (_course$category2 = course.category) === null || _course$category2 === void 0 ? void 0 : _course$category2.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 179,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"rating\",\n                      children: [[1, 2, 3, 4, 5].map(star => /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-star\"\n                      }, star, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 182,\n                        columnNumber: 27\n                      }, this)), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"ms-1 text-dark\",\n                        children: \"5.0\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"card-title\",\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      to: `/courses/${course.id}`,\n                      className: \"text-decoration-none text-dark\",\n                      children: truncateWords(course.title, 8)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 188,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"card-text\",\n                    children: truncateWords(course.description, 15)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"course-instructor mb-3\",\n                    children: [getUserAvatar(course.instructor, 36), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"ms-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"text-muted\",\n                        children: \"Instructor\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 196,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"mb-0 fw-medium\",\n                        children: (_course$instructor = course.instructor) !== null && _course$instructor !== void 0 && _course$instructor.first_name && (_course$instructor2 = course.instructor) !== null && _course$instructor2 !== void 0 && _course$instructor2.last_name ? `${course.instructor.first_name} ${course.instructor.last_name}` : (_course$instructor3 = course.instructor) === null || _course$instructor3 === void 0 ? void 0 : _course$instructor3.username\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 197,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-between align-items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"fw-bold text-primary\",\n                      children: formatPrice(course.price)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 205,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Link, {\n                      to: `/courses/${course.id}`,\n                      className: \"btn btn-sm btn-primary\",\n                      children: \"View Course\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this)\n            }, course.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-center mb-5\",\n          children: \"How Pathshala Works\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row g-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card role-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"role-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-user-shield\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"For Admins\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"text-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Manage users and assign roles\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Create and organize course categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Access platform-wide statistics\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Moderate content and maintain quality\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), (user === null || user === void 0 ? void 0 : user.role) === 'admin' ? /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/dashboard\",\n                className: \"btn btn-outline-primary mt-3\",\n                children: \"Admin Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"btn btn-outline-primary mt-3\",\n                children: \"Admin Portal\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card role-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"role-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-chalkboard-teacher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"For Teachers\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"text-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Create and manage your courses\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Upload lessons and learning materials\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Track student progress\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Interact with your students\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this), (user === null || user === void 0 ? void 0 : user.role) === 'teacher' ? /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/teacher/courses\",\n                className: \"btn btn-outline-primary mt-3\",\n                children: \"My Courses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/signup?role=teacher\",\n                className: \"btn btn-outline-primary mt-3\",\n                children: \"Become a Teacher\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card role-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"role-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-user-graduate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"For Students\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"text-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Discover and enroll in courses\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Learn at your own pace\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Track your learning progress\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Earn certificates of completion\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this), (user === null || user === void 0 ? void 0 : user.role) === 'student' ? /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/dashboard\",\n                className: \"btn btn-outline-primary mt-3\",\n                children: \"My Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/signup?role=student\",\n                className: \"btn btn-outline-primary mt-3\",\n                children: \"Start Learning\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"stats-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-center mb-5\",\n          children: \"Pathshala by the Numbers\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3 col-6 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-value\",\n              children: homeData.total_courses || \"500+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Courses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3 col-6 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-value\",\n              children: homeData.total_teachers || \"150+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Expert Teachers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3 col-6 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-value\",\n              children: homeData.total_students || \"25,000+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Students\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3 col-6 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-value\",\n              children: \"98%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Satisfaction Rate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-5 bg-primary text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mb-4\",\n          children: \"Ready to Start Your Learning Journey?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"lead mb-4\",\n          children: \"Join thousands of students and teachers on Pathshala today.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex flex-wrap justify-content-center\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/courses\",\n            className: \"btn btn-light btn-lg me-3 mb-3\",\n            children: \"Browse Courses\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/signup\",\n            className: \"btn btn-outline-light btn-lg mb-3\",\n            children: \"Sign Up for Free\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Home, \"sBl2BIxXipkheT+s1BvDjknll4k=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "useAuth", "homeAPI", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Home", "_s", "user", "isAuthenticated", "navigate", "homeData", "setHomeData", "featured_courses", "categories", "total_courses", "total_students", "total_teachers", "searchQuery", "setSearch<PERSON>uery", "loading", "setLoading", "loadHomeData", "response", "getHomeData", "data", "error", "console", "handleSearch", "e", "preventDefault", "trim", "encodeURIComponent", "formatPrice", "price", "getUserAvatar", "size", "initials", "first_name", "last_name", "toUpperCase", "username", "className", "style", "width", "height", "fontSize", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "truncateWords", "text", "wordLimit", "words", "split", "length", "slice", "join", "getCourseImage", "course", "_course$category", "banner", "categoryImages", "category", "title", "role", "onSubmit", "type", "placeholder", "value", "onChange", "target", "to", "src", "alt", "map", "id", "_course$category2", "_course$instructor", "_course$instructor2", "_course$instructor3", "star", "description", "instructor", "href", "_c", "$RefreshReg$"], "sources": ["E:/Downloads/lms_backend/frontend/src/pages/Home.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { homeAPI } from '../services/api';\n\nconst Home = () => {\n  const { user, isAuthenticated } = useAuth();\n  const navigate = useNavigate();\n  const [homeData, setHomeData] = useState({\n    featured_courses: [],\n    categories: [],\n    total_courses: 0,\n    total_students: 0,\n    total_teachers: 0,\n  });\n  const [searchQuery, setSearchQuery] = useState('');\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    loadHomeData();\n  }, []);\n\n  const loadHomeData = async () => {\n    try {\n      const response = await homeAPI.getHomeData();\n      setHomeData(response.data);\n    } catch (error) {\n      console.error('Failed to load home data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      navigate(`/courses?search=${encodeURIComponent(searchQuery.trim())}`);\n    }\n  };\n\n  const formatPrice = (price) => {\n    return price === 0 ? 'Free' : `$${price}`;\n  };\n\n  const getUserAvatar = (user, size = 36) => {\n    if (!user) return null;\n    \n    const initials = user.first_name && user.last_name \n      ? `${user.first_name[0]}${user.last_name[0]}`.toUpperCase()\n      : user.username[0].toUpperCase();\n    \n    return (\n      <div \n        className=\"user-avatar d-flex align-items-center justify-content-center text-white rounded-circle\"\n        style={{ width: size, height: size, fontSize: size * 0.4 }}\n      >\n        {initials}\n      </div>\n    );\n  };\n\n  const truncateWords = (text, wordLimit) => {\n    if (!text) return '';\n    const words = text.split(' ');\n    if (words.length <= wordLimit) return text;\n    return words.slice(0, wordLimit).join(' ') + '...';\n  };\n\n  const getCourseImage = (course) => {\n    if (course.banner) {\n      return course.banner;\n    }\n\n    // Fallback images based on category\n    const categoryImages = {\n      'Programming': 'https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&h=240&q=80',\n      'Web Development': 'https://images.unsplash.com/photo-1547658719-da2b51169166?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&h=240&q=80',\n      'Data Science': 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&h=240&q=80',\n      'Mobile Development': 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&h=240&q=80',\n      'Machine Learning': 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&h=240&q=80'\n    };\n\n    return categoryImages[course.category?.title] || 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&h=240&q=80';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"d-flex justify-content-center align-items-center\" style={{ height: '50vh' }}>\n        <div className=\"spinner-border text-primary\" role=\"status\">\n          <span className=\"visually-hidden\">Loading...</span>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      {/* Hero Section */}\n      <section className=\"hero\">\n        <div className=\"container\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-lg-6\">\n              <h1>Learn Without Limits</h1>\n              <p className=\"lead mb-4\">\n                Discover, learn, and grow with Pathshala. Join thousands of students and teachers in our interactive learning platform.\n              </p>\n              \n              {/* Hero Search Form */}\n              <form className=\"search-form mb-4\" onSubmit={handleSearch}>\n                <div className=\"input-group\">\n                  <input \n                    type=\"text\" \n                    className=\"form-control form-control-lg\" \n                    placeholder=\"What do you want to learn today?\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                  />\n                  <button className=\"btn btn-primary\" type=\"submit\">Search</button>\n                </div>\n              </form>\n              \n              <div className=\"d-flex flex-wrap\">\n                <Link to=\"/courses\" className=\"btn btn-light btn-lg me-3 mb-3\">Explore Courses</Link>\n                {!isAuthenticated && (\n                  <Link to=\"/signup?role=teacher\" className=\"btn btn-outline-light btn-lg mb-3\">\n                    Become a Teacher\n                  </Link>\n                )}\n              </div>\n            </div>\n            <div className=\"col-lg-6\">\n              <img \n                src=\"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1350&q=80\" \n                alt=\"Students learning online\" \n                className=\"img-fluid rounded shadow-lg\"\n              />\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Categories */}\n      <section className=\"py-5\">\n        <div className=\"container\">\n          <h2 className=\"text-center mb-4\">Browse Categories</h2>\n          <div className=\"d-flex flex-wrap justify-content-center mb-4\">\n            <Link to=\"/courses\" className=\"category-pill bg-light active text-decoration-none\">All</Link>\n            {homeData.categories.map(category => (\n              <Link \n                key={category.id}\n                to={`/courses?category=${category.id}`} \n                className=\"category-pill bg-light text-decoration-none\"\n              >\n                {category.title}\n              </Link>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Courses */}\n      <section className=\"py-5 bg-light\">\n        <div className=\"container\">\n          <div className=\"d-flex justify-content-between align-items-center mb-4\">\n            <h2>Featured Courses</h2>\n            <Link to=\"/courses\" className=\"btn btn-outline-primary\">View All Courses</Link>\n          </div>\n          <div className=\"row g-4\">\n            {homeData.featured_courses.slice(0, 6).map(course => (\n              <div key={course.id} className=\"col-md-6 col-lg-4\">\n                <div className=\"card course-card h-100\">\n                  <img \n                    src={course.banner || \"https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&h=240&q=80\"} \n                    className=\"card-img-top\" \n                    alt={course.title}\n                  />\n                  <div className=\"card-body\">\n                    <div className=\"d-flex justify-content-between align-items-center mb-2\">\n                      <span className=\"badge badge-category\">{course.category?.title}</span>\n                      <div className=\"rating\">\n                        {[1,2,3,4,5].map(star => (\n                          <i key={star} className=\"fas fa-star\"></i>\n                        ))}\n                        <span className=\"ms-1 text-dark\">5.0</span>\n                      </div>\n                    </div>\n                    <h5 className=\"card-title\">\n                      <Link to={`/courses/${course.id}`} className=\"text-decoration-none text-dark\">\n                        {truncateWords(course.title, 8)}\n                      </Link>\n                    </h5>\n                    <p className=\"card-text\">{truncateWords(course.description, 15)}</p>\n                    <div className=\"course-instructor mb-3\">\n                      {getUserAvatar(course.instructor, 36)}\n                      <div className=\"ms-2\">\n                        <small className=\"text-muted\">Instructor</small>\n                        <p className=\"mb-0 fw-medium\">\n                          {course.instructor?.first_name && course.instructor?.last_name \n                            ? `${course.instructor.first_name} ${course.instructor.last_name}`\n                            : course.instructor?.username}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"d-flex justify-content-between align-items-center\">\n                      <span className=\"fw-bold text-primary\">{formatPrice(course.price)}</span>\n                      <Link to={`/courses/${course.id}`} className=\"btn btn-sm btn-primary\">\n                        View Course\n                      </Link>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* How It Works */}\n      <section className=\"py-5\">\n        <div className=\"container\">\n          <h2 className=\"text-center mb-5\">How Pathshala Works</h2>\n          <div className=\"row g-4\">\n            {/* Admin Role */}\n            <div className=\"col-md-4\">\n              <div className=\"card role-card\">\n                <div className=\"role-icon\">\n                  <i className=\"fas fa-user-shield\"></i>\n                </div>\n                <h3>For Admins</h3>\n                <ul className=\"text-start\">\n                  <li>Manage users and assign roles</li>\n                  <li>Create and organize course categories</li>\n                  <li>Access platform-wide statistics</li>\n                  <li>Moderate content and maintain quality</li>\n                </ul>\n                {user?.role === 'admin' ? (\n                  <Link to=\"/dashboard\" className=\"btn btn-outline-primary mt-3\">Admin Dashboard</Link>\n                ) : (\n                  <a href=\"#\" className=\"btn btn-outline-primary mt-3\">Admin Portal</a>\n                )}\n              </div>\n            </div>\n            \n            {/* Teacher Role */}\n            <div className=\"col-md-4\">\n              <div className=\"card role-card\">\n                <div className=\"role-icon\">\n                  <i className=\"fas fa-chalkboard-teacher\"></i>\n                </div>\n                <h3>For Teachers</h3>\n                <ul className=\"text-start\">\n                  <li>Create and manage your courses</li>\n                  <li>Upload lessons and learning materials</li>\n                  <li>Track student progress</li>\n                  <li>Interact with your students</li>\n                </ul>\n                {user?.role === 'teacher' ? (\n                  <Link to=\"/teacher/courses\" className=\"btn btn-outline-primary mt-3\">My Courses</Link>\n                ) : (\n                  <Link to=\"/signup?role=teacher\" className=\"btn btn-outline-primary mt-3\">Become a Teacher</Link>\n                )}\n              </div>\n            </div>\n            \n            {/* Student Role */}\n            <div className=\"col-md-4\">\n              <div className=\"card role-card\">\n                <div className=\"role-icon\">\n                  <i className=\"fas fa-user-graduate\"></i>\n                </div>\n                <h3>For Students</h3>\n                <ul className=\"text-start\">\n                  <li>Discover and enroll in courses</li>\n                  <li>Learn at your own pace</li>\n                  <li>Track your learning progress</li>\n                  <li>Earn certificates of completion</li>\n                </ul>\n                {user?.role === 'student' ? (\n                  <Link to=\"/dashboard\" className=\"btn btn-outline-primary mt-3\">My Dashboard</Link>\n                ) : (\n                  <Link to=\"/signup?role=student\" className=\"btn btn-outline-primary mt-3\">Start Learning</Link>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Statistics */}\n      <section className=\"stats-section\">\n        <div className=\"container\">\n          <h2 className=\"text-center mb-5\">Pathshala by the Numbers</h2>\n          <div className=\"row text-center\">\n            <div className=\"col-md-3 col-6 mb-4\">\n              <div className=\"stat-value\">{homeData.total_courses || \"500+\"}</div>\n              <div className=\"stat-label\">Courses</div>\n            </div>\n            <div className=\"col-md-3 col-6 mb-4\">\n              <div className=\"stat-value\">{homeData.total_teachers || \"150+\"}</div>\n              <div className=\"stat-label\">Expert Teachers</div>\n            </div>\n            <div className=\"col-md-3 col-6 mb-4\">\n              <div className=\"stat-value\">{homeData.total_students || \"25,000+\"}</div>\n              <div className=\"stat-label\">Students</div>\n            </div>\n            <div className=\"col-md-3 col-6 mb-4\">\n              <div className=\"stat-value\">98%</div>\n              <div className=\"stat-label\">Satisfaction Rate</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-5 bg-primary text-white\">\n        <div className=\"container text-center\">\n          <h2 className=\"mb-4\">Ready to Start Your Learning Journey?</h2>\n          <p className=\"lead mb-4\">Join thousands of students and teachers on Pathshala today.</p>\n          <div className=\"d-flex flex-wrap justify-content-center\">\n            <Link to=\"/courses\" className=\"btn btn-light btn-lg me-3 mb-3\">Browse Courses</Link>\n            {!isAuthenticated && (\n              <Link to=\"/signup\" className=\"btn btn-outline-light btn-lg mb-3\">Sign Up for Free</Link>\n            )}\n          </div>\n        </div>\n      </section>\n    </>\n  );\n};\n\nexport default Home;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1C,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC3C,MAAMU,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC;IACvCiB,gBAAgB,EAAE,EAAE;IACpBC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdyB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMtB,OAAO,CAACuB,WAAW,CAAC,CAAC;MAC5CZ,WAAW,CAACW,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACRL,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMO,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIZ,WAAW,CAACa,IAAI,CAAC,CAAC,EAAE;MACtBrB,QAAQ,CAAC,mBAAmBsB,kBAAkB,CAACd,WAAW,CAACa,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;IACvE;EACF,CAAC;EAED,MAAME,WAAW,GAAIC,KAAK,IAAK;IAC7B,OAAOA,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG,IAAIA,KAAK,EAAE;EAC3C,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAC3B,IAAI,EAAE4B,IAAI,GAAG,EAAE,KAAK;IACzC,IAAI,CAAC5B,IAAI,EAAE,OAAO,IAAI;IAEtB,MAAM6B,QAAQ,GAAG7B,IAAI,CAAC8B,UAAU,IAAI9B,IAAI,CAAC+B,SAAS,GAC9C,GAAG/B,IAAI,CAAC8B,UAAU,CAAC,CAAC,CAAC,GAAG9B,IAAI,CAAC+B,SAAS,CAAC,CAAC,CAAC,EAAE,CAACC,WAAW,CAAC,CAAC,GACzDhC,IAAI,CAACiC,QAAQ,CAAC,CAAC,CAAC,CAACD,WAAW,CAAC,CAAC;IAElC,oBACErC,OAAA;MACEuC,SAAS,EAAC,wFAAwF;MAClGC,KAAK,EAAE;QAAEC,KAAK,EAAER,IAAI;QAAES,MAAM,EAAET,IAAI;QAAEU,QAAQ,EAAEV,IAAI,GAAG;MAAI,CAAE;MAAAW,QAAA,EAE1DV;IAAQ;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV,CAAC;EAED,MAAMC,aAAa,GAAGA,CAACC,IAAI,EAAEC,SAAS,KAAK;IACzC,IAAI,CAACD,IAAI,EAAE,OAAO,EAAE;IACpB,MAAME,KAAK,GAAGF,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC;IAC7B,IAAID,KAAK,CAACE,MAAM,IAAIH,SAAS,EAAE,OAAOD,IAAI;IAC1C,OAAOE,KAAK,CAACG,KAAK,CAAC,CAAC,EAAEJ,SAAS,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK;EACpD,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IAAA,IAAAC,gBAAA;IACjC,IAAID,MAAM,CAACE,MAAM,EAAE;MACjB,OAAOF,MAAM,CAACE,MAAM;IACtB;;IAEA;IACA,MAAMC,cAAc,GAAG;MACrB,aAAa,EAAE,mHAAmH;MAClI,iBAAiB,EAAE,gHAAgH;MACnI,cAAc,EAAE,gHAAgH;MAChI,oBAAoB,EAAE,mHAAmH;MACzI,kBAAkB,EAAE;IACtB,CAAC;IAED,OAAOA,cAAc,EAAAF,gBAAA,GAACD,MAAM,CAACI,QAAQ,cAAAH,gBAAA,uBAAfA,gBAAA,CAAiBI,KAAK,CAAC,IAAI,mHAAmH;EACtK,CAAC;EAED,IAAI9C,OAAO,EAAE;IACX,oBACEjB,OAAA;MAAKuC,SAAS,EAAC,kDAAkD;MAACC,KAAK,EAAE;QAAEE,MAAM,EAAE;MAAO,CAAE;MAAAE,QAAA,eAC1F5C,OAAA;QAAKuC,SAAS,EAAC,6BAA6B;QAACyB,IAAI,EAAC,QAAQ;QAAApB,QAAA,eACxD5C,OAAA;UAAMuC,SAAS,EAAC,iBAAiB;UAAAK,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhD,OAAA,CAAAE,SAAA;IAAA0C,QAAA,gBAEE5C,OAAA;MAASuC,SAAS,EAAC,MAAM;MAAAK,QAAA,eACvB5C,OAAA;QAAKuC,SAAS,EAAC,WAAW;QAAAK,QAAA,eACxB5C,OAAA;UAAKuC,SAAS,EAAC,wBAAwB;UAAAK,QAAA,gBACrC5C,OAAA;YAAKuC,SAAS,EAAC,UAAU;YAAAK,QAAA,gBACvB5C,OAAA;cAAA4C,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7BhD,OAAA;cAAGuC,SAAS,EAAC,WAAW;cAAAK,QAAA,EAAC;YAEzB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAGJhD,OAAA;cAAMuC,SAAS,EAAC,kBAAkB;cAAC0B,QAAQ,EAAExC,YAAa;cAAAmB,QAAA,eACxD5C,OAAA;gBAAKuC,SAAS,EAAC,aAAa;gBAAAK,QAAA,gBAC1B5C,OAAA;kBACEkE,IAAI,EAAC,MAAM;kBACX3B,SAAS,EAAC,8BAA8B;kBACxC4B,WAAW,EAAC,kCAAkC;kBAC9CC,KAAK,EAAErD,WAAY;kBACnBsD,QAAQ,EAAG3C,CAAC,IAAKV,cAAc,CAACU,CAAC,CAAC4C,MAAM,CAACF,KAAK;gBAAE;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACFhD,OAAA;kBAAQuC,SAAS,EAAC,iBAAiB;kBAAC2B,IAAI,EAAC,QAAQ;kBAAAtB,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEPhD,OAAA;cAAKuC,SAAS,EAAC,kBAAkB;cAAAK,QAAA,gBAC/B5C,OAAA,CAACL,IAAI;gBAAC4E,EAAE,EAAC,UAAU;gBAAChC,SAAS,EAAC,gCAAgC;gBAAAK,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACpF,CAAC1C,eAAe,iBACfN,OAAA,CAACL,IAAI;gBAAC4E,EAAE,EAAC,sBAAsB;gBAAChC,SAAS,EAAC,mCAAmC;gBAAAK,QAAA,EAAC;cAE9E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhD,OAAA;YAAKuC,SAAS,EAAC,UAAU;YAAAK,QAAA,eACvB5C,OAAA;cACEwE,GAAG,EAAC,gKAAgK;cACpKC,GAAG,EAAC,0BAA0B;cAC9BlC,SAAS,EAAC;YAA6B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhD,OAAA;MAASuC,SAAS,EAAC,MAAM;MAAAK,QAAA,eACvB5C,OAAA;QAAKuC,SAAS,EAAC,WAAW;QAAAK,QAAA,gBACxB5C,OAAA;UAAIuC,SAAS,EAAC,kBAAkB;UAAAK,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvDhD,OAAA;UAAKuC,SAAS,EAAC,8CAA8C;UAAAK,QAAA,gBAC3D5C,OAAA,CAACL,IAAI;YAAC4E,EAAE,EAAC,UAAU;YAAChC,SAAS,EAAC,oDAAoD;YAAAK,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAC5FxC,QAAQ,CAACG,UAAU,CAAC+D,GAAG,CAACZ,QAAQ,iBAC/B9D,OAAA,CAACL,IAAI;YAEH4E,EAAE,EAAE,qBAAqBT,QAAQ,CAACa,EAAE,EAAG;YACvCpC,SAAS,EAAC,6CAA6C;YAAAK,QAAA,EAEtDkB,QAAQ,CAACC;UAAK,GAJVD,QAAQ,CAACa,EAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKZ,CACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhD,OAAA;MAASuC,SAAS,EAAC,eAAe;MAAAK,QAAA,eAChC5C,OAAA;QAAKuC,SAAS,EAAC,WAAW;QAAAK,QAAA,gBACxB5C,OAAA;UAAKuC,SAAS,EAAC,wDAAwD;UAAAK,QAAA,gBACrE5C,OAAA;YAAA4C,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBhD,OAAA,CAACL,IAAI;YAAC4E,EAAE,EAAC,UAAU;YAAChC,SAAS,EAAC,yBAAyB;YAAAK,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eACNhD,OAAA;UAAKuC,SAAS,EAAC,SAAS;UAAAK,QAAA,EACrBpC,QAAQ,CAACE,gBAAgB,CAAC6C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACmB,GAAG,CAAChB,MAAM;YAAA,IAAAkB,iBAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA;YAAA,oBAC/C/E,OAAA;cAAqBuC,SAAS,EAAC,mBAAmB;cAAAK,QAAA,eAChD5C,OAAA;gBAAKuC,SAAS,EAAC,wBAAwB;gBAAAK,QAAA,gBACrC5C,OAAA;kBACEwE,GAAG,EAAEd,MAAM,CAACE,MAAM,IAAI,mHAAoH;kBAC1IrB,SAAS,EAAC,cAAc;kBACxBkC,GAAG,EAAEf,MAAM,CAACK;gBAAM;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACFhD,OAAA;kBAAKuC,SAAS,EAAC,WAAW;kBAAAK,QAAA,gBACxB5C,OAAA;oBAAKuC,SAAS,EAAC,wDAAwD;oBAAAK,QAAA,gBACrE5C,OAAA;sBAAMuC,SAAS,EAAC,sBAAsB;sBAAAK,QAAA,GAAAgC,iBAAA,GAAElB,MAAM,CAACI,QAAQ,cAAAc,iBAAA,uBAAfA,iBAAA,CAAiBb;oBAAK;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACtEhD,OAAA;sBAAKuC,SAAS,EAAC,QAAQ;sBAAAK,QAAA,GACpB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC8B,GAAG,CAACM,IAAI,iBACnBhF,OAAA;wBAAcuC,SAAS,EAAC;sBAAa,GAA7ByC,IAAI;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAA6B,CAC1C,CAAC,eACFhD,OAAA;wBAAMuC,SAAS,EAAC,gBAAgB;wBAAAK,QAAA,EAAC;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNhD,OAAA;oBAAIuC,SAAS,EAAC,YAAY;oBAAAK,QAAA,eACxB5C,OAAA,CAACL,IAAI;sBAAC4E,EAAE,EAAE,YAAYb,MAAM,CAACiB,EAAE,EAAG;sBAACpC,SAAS,EAAC,gCAAgC;sBAAAK,QAAA,EAC1EK,aAAa,CAACS,MAAM,CAACK,KAAK,EAAE,CAAC;oBAAC;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACLhD,OAAA;oBAAGuC,SAAS,EAAC,WAAW;oBAAAK,QAAA,EAAEK,aAAa,CAACS,MAAM,CAACuB,WAAW,EAAE,EAAE;kBAAC;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpEhD,OAAA;oBAAKuC,SAAS,EAAC,wBAAwB;oBAAAK,QAAA,GACpCZ,aAAa,CAAC0B,MAAM,CAACwB,UAAU,EAAE,EAAE,CAAC,eACrClF,OAAA;sBAAKuC,SAAS,EAAC,MAAM;sBAAAK,QAAA,gBACnB5C,OAAA;wBAAOuC,SAAS,EAAC,YAAY;wBAAAK,QAAA,EAAC;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAChDhD,OAAA;wBAAGuC,SAAS,EAAC,gBAAgB;wBAAAK,QAAA,EAC1B,CAAAiC,kBAAA,GAAAnB,MAAM,CAACwB,UAAU,cAAAL,kBAAA,eAAjBA,kBAAA,CAAmB1C,UAAU,KAAA2C,mBAAA,GAAIpB,MAAM,CAACwB,UAAU,cAAAJ,mBAAA,eAAjBA,mBAAA,CAAmB1C,SAAS,GAC1D,GAAGsB,MAAM,CAACwB,UAAU,CAAC/C,UAAU,IAAIuB,MAAM,CAACwB,UAAU,CAAC9C,SAAS,EAAE,IAAA2C,mBAAA,GAChErB,MAAM,CAACwB,UAAU,cAAAH,mBAAA,uBAAjBA,mBAAA,CAAmBzC;sBAAQ;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNhD,OAAA;oBAAKuC,SAAS,EAAC,mDAAmD;oBAAAK,QAAA,gBAChE5C,OAAA;sBAAMuC,SAAS,EAAC,sBAAsB;sBAAAK,QAAA,EAAEd,WAAW,CAAC4B,MAAM,CAAC3B,KAAK;oBAAC;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACzEhD,OAAA,CAACL,IAAI;sBAAC4E,EAAE,EAAE,YAAYb,MAAM,CAACiB,EAAE,EAAG;sBAACpC,SAAS,EAAC,wBAAwB;sBAAAK,QAAA,EAAC;oBAEtE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GAzCEU,MAAM,CAACiB,EAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0Cd,CAAC;UAAA,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhD,OAAA;MAASuC,SAAS,EAAC,MAAM;MAAAK,QAAA,eACvB5C,OAAA;QAAKuC,SAAS,EAAC,WAAW;QAAAK,QAAA,gBACxB5C,OAAA;UAAIuC,SAAS,EAAC,kBAAkB;UAAAK,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzDhD,OAAA;UAAKuC,SAAS,EAAC,SAAS;UAAAK,QAAA,gBAEtB5C,OAAA;YAAKuC,SAAS,EAAC,UAAU;YAAAK,QAAA,eACvB5C,OAAA;cAAKuC,SAAS,EAAC,gBAAgB;cAAAK,QAAA,gBAC7B5C,OAAA;gBAAKuC,SAAS,EAAC,WAAW;gBAAAK,QAAA,eACxB5C,OAAA;kBAAGuC,SAAS,EAAC;gBAAoB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACNhD,OAAA;gBAAA4C,QAAA,EAAI;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBhD,OAAA;gBAAIuC,SAAS,EAAC,YAAY;gBAAAK,QAAA,gBACxB5C,OAAA;kBAAA4C,QAAA,EAAI;gBAA6B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtChD,OAAA;kBAAA4C,QAAA,EAAI;gBAAqC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9ChD,OAAA;kBAAA4C,QAAA,EAAI;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxChD,OAAA;kBAAA4C,QAAA,EAAI;gBAAqC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,EACJ,CAAA3C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,IAAI,MAAK,OAAO,gBACrBhE,OAAA,CAACL,IAAI;gBAAC4E,EAAE,EAAC,YAAY;gBAAChC,SAAS,EAAC,8BAA8B;gBAAAK,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAErFhD,OAAA;gBAAGmF,IAAI,EAAC,GAAG;gBAAC5C,SAAS,EAAC,8BAA8B;gBAAAK,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACrE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhD,OAAA;YAAKuC,SAAS,EAAC,UAAU;YAAAK,QAAA,eACvB5C,OAAA;cAAKuC,SAAS,EAAC,gBAAgB;cAAAK,QAAA,gBAC7B5C,OAAA;gBAAKuC,SAAS,EAAC,WAAW;gBAAAK,QAAA,eACxB5C,OAAA;kBAAGuC,SAAS,EAAC;gBAA2B;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACNhD,OAAA;gBAAA4C,QAAA,EAAI;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrBhD,OAAA;gBAAIuC,SAAS,EAAC,YAAY;gBAAAK,QAAA,gBACxB5C,OAAA;kBAAA4C,QAAA,EAAI;gBAA8B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvChD,OAAA;kBAAA4C,QAAA,EAAI;gBAAqC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9ChD,OAAA;kBAAA4C,QAAA,EAAI;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/BhD,OAAA;kBAAA4C,QAAA,EAAI;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,EACJ,CAAA3C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,IAAI,MAAK,SAAS,gBACvBhE,OAAA,CAACL,IAAI;gBAAC4E,EAAE,EAAC,kBAAkB;gBAAChC,SAAS,EAAC,8BAA8B;gBAAAK,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAEtFhD,OAAA,CAACL,IAAI;gBAAC4E,EAAE,EAAC,sBAAsB;gBAAChC,SAAS,EAAC,8BAA8B;gBAAAK,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAChG;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhD,OAAA;YAAKuC,SAAS,EAAC,UAAU;YAAAK,QAAA,eACvB5C,OAAA;cAAKuC,SAAS,EAAC,gBAAgB;cAAAK,QAAA,gBAC7B5C,OAAA;gBAAKuC,SAAS,EAAC,WAAW;gBAAAK,QAAA,eACxB5C,OAAA;kBAAGuC,SAAS,EAAC;gBAAsB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACNhD,OAAA;gBAAA4C,QAAA,EAAI;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrBhD,OAAA;gBAAIuC,SAAS,EAAC,YAAY;gBAAAK,QAAA,gBACxB5C,OAAA;kBAAA4C,QAAA,EAAI;gBAA8B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvChD,OAAA;kBAAA4C,QAAA,EAAI;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/BhD,OAAA;kBAAA4C,QAAA,EAAI;gBAA4B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrChD,OAAA;kBAAA4C,QAAA,EAAI;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,EACJ,CAAA3C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,IAAI,MAAK,SAAS,gBACvBhE,OAAA,CAACL,IAAI;gBAAC4E,EAAE,EAAC,YAAY;gBAAChC,SAAS,EAAC,8BAA8B;gBAAAK,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAElFhD,OAAA,CAACL,IAAI;gBAAC4E,EAAE,EAAC,sBAAsB;gBAAChC,SAAS,EAAC,8BAA8B;gBAAAK,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAC9F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhD,OAAA;MAASuC,SAAS,EAAC,eAAe;MAAAK,QAAA,eAChC5C,OAAA;QAAKuC,SAAS,EAAC,WAAW;QAAAK,QAAA,gBACxB5C,OAAA;UAAIuC,SAAS,EAAC,kBAAkB;UAAAK,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DhD,OAAA;UAAKuC,SAAS,EAAC,iBAAiB;UAAAK,QAAA,gBAC9B5C,OAAA;YAAKuC,SAAS,EAAC,qBAAqB;YAAAK,QAAA,gBAClC5C,OAAA;cAAKuC,SAAS,EAAC,YAAY;cAAAK,QAAA,EAAEpC,QAAQ,CAACI,aAAa,IAAI;YAAM;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpEhD,OAAA;cAAKuC,SAAS,EAAC,YAAY;cAAAK,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACNhD,OAAA;YAAKuC,SAAS,EAAC,qBAAqB;YAAAK,QAAA,gBAClC5C,OAAA;cAAKuC,SAAS,EAAC,YAAY;cAAAK,QAAA,EAAEpC,QAAQ,CAACM,cAAc,IAAI;YAAM;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrEhD,OAAA;cAAKuC,SAAS,EAAC,YAAY;cAAAK,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNhD,OAAA;YAAKuC,SAAS,EAAC,qBAAqB;YAAAK,QAAA,gBAClC5C,OAAA;cAAKuC,SAAS,EAAC,YAAY;cAAAK,QAAA,EAAEpC,QAAQ,CAACK,cAAc,IAAI;YAAS;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxEhD,OAAA;cAAKuC,SAAS,EAAC,YAAY;cAAAK,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACNhD,OAAA;YAAKuC,SAAS,EAAC,qBAAqB;YAAAK,QAAA,gBAClC5C,OAAA;cAAKuC,SAAS,EAAC,YAAY;cAAAK,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrChD,OAAA;cAAKuC,SAAS,EAAC,YAAY;cAAAK,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhD,OAAA;MAASuC,SAAS,EAAC,4BAA4B;MAAAK,QAAA,eAC7C5C,OAAA;QAAKuC,SAAS,EAAC,uBAAuB;QAAAK,QAAA,gBACpC5C,OAAA;UAAIuC,SAAS,EAAC,MAAM;UAAAK,QAAA,EAAC;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/DhD,OAAA;UAAGuC,SAAS,EAAC,WAAW;UAAAK,QAAA,EAAC;QAA2D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACxFhD,OAAA;UAAKuC,SAAS,EAAC,yCAAyC;UAAAK,QAAA,gBACtD5C,OAAA,CAACL,IAAI;YAAC4E,EAAE,EAAC,UAAU;YAAChC,SAAS,EAAC,gCAAgC;YAAAK,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACnF,CAAC1C,eAAe,iBACfN,OAAA,CAACL,IAAI;YAAC4E,EAAE,EAAC,SAAS;YAAChC,SAAS,EAAC,mCAAmC;YAAAK,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA,eACV,CAAC;AAEP,CAAC;AAAC5C,EAAA,CAnUID,IAAI;EAAA,QAC0BN,OAAO,EACxBD,WAAW;AAAA;AAAAwF,EAAA,GAFxBjF,IAAI;AAqUV,eAAeA,IAAI;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}