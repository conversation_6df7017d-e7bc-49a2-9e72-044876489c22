{"ast": null, "code": "var _jsxFileName = \"E:\\\\Downloads\\\\lms_backend\\\\frontend\\\\src\\\\components\\\\courses\\\\CourseDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, Link, useNavigate } from 'react-router-dom';\nimport { courseAPI } from '../../services/api';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CourseDetail = () => {\n  _s();\n  var _course$category2, _course$instructor, _course$instructor2, _course$instructor3;\n  const {\n    courseId\n  } = useParams();\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const navigate = useNavigate();\n  const [courseData, setCourseData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [enrolling, setEnrolling] = useState(false);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    loadCourseDetail();\n  }, [courseId]);\n  const loadCourseDetail = async () => {\n    try {\n      setLoading(true);\n      const response = await courseAPI.getCourseDetail(courseId);\n      setCourseData(response.data);\n    } catch (error) {\n      console.error('Failed to load course detail:', error);\n      setError('Failed to load course details. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleEnroll = async () => {\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n    if ((user === null || user === void 0 ? void 0 : user.role) !== 'student') {\n      alert('Only students can enroll in courses');\n      return;\n    }\n    try {\n      setEnrolling(true);\n      const response = await courseAPI.enrollCourse(courseId);\n      alert(response.data.message);\n      // Reload course data to update enrollment status\n      loadCourseDetail();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'Failed to enroll in course';\n      alert(errorMessage);\n    } finally {\n      setEnrolling(false);\n    }\n  };\n  const formatPrice = price => {\n    return price === 0 ? 'Free' : `$${price}`;\n  };\n  const getUserAvatar = (user, size = 48) => {\n    if (!user) return null;\n    const initials = user.first_name && user.last_name ? `${user.first_name[0]}${user.last_name[0]}`.toUpperCase() : user.username[0].toUpperCase();\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-avatar d-flex align-items-center justify-content-center text-white rounded-circle\",\n      style: {\n        width: size,\n        height: size,\n        fontSize: size * 0.4\n      },\n      children: initials\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this);\n  };\n  const getCourseImage = course => {\n    var _course$category;\n    if (course.banner) {\n      return course.banner;\n    }\n\n    // Fallback images based on category\n    const categoryImages = {\n      'Programming': 'https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&h=400&q=80',\n      'Web Development': 'https://images.unsplash.com/photo-1547658719-da2b51169166?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&h=400&q=80',\n      'Data Science': 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&h=400&q=80',\n      'Mobile Development': 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&h=400&q=80',\n      'Machine Learning': 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&h=400&q=80'\n    };\n    return categoryImages[(_course$category = course.category) === null || _course$category === void 0 ? void 0 : _course$category.title] || 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&h=400&q=80';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container my-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center align-items-center\",\n        style: {\n          height: '50vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container my-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        role: \"alert\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"alert-heading\",\n          children: \"Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/courses\",\n          className: \"btn btn-primary\",\n          children: \"Back to Courses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this);\n  }\n  if (!courseData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container my-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-warning\",\n        role: \"alert\",\n        children: [\"Course not found.\", /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/courses\",\n          className: \"btn btn-primary ms-3\",\n          children: \"Back to Courses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this);\n  }\n  const {\n    course,\n    is_enrolled,\n    enrollment,\n    can_access_content\n  } = courseData;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container my-5\",\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      \"aria-label\": \"breadcrumb\",\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"ol\", {\n        className: \"breadcrumb\",\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          className: \"breadcrumb-item\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          className: \"breadcrumb-item\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/courses\",\n            children: \"Courses\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          className: \"breadcrumb-item active\",\n          \"aria-current\": \"page\",\n          children: course.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-lg-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: getCourseImage(course),\n            className: \"card-img-top\",\n            alt: course.title,\n            style: {\n              height: '300px',\n              objectFit: 'cover'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between align-items-start mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge badge-category mb-2\",\n                  children: (_course$category2 = course.category) === null || _course$category2 === void 0 ? void 0 : _course$category2.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"card-title h3\",\n                  children: course.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-end\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"rating mb-2\",\n                  children: [[1, 2, 3, 4, 5].map(star => /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-star text-warning\"\n                  }, star, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 23\n                  }, this)), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ms-1 text-dark\",\n                    children: \"5.0 (128 reviews)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"h4 text-primary mb-0\",\n                  children: formatPrice(course.price)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"card-text\",\n              children: course.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row text-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border-end\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-primary\",\n                    children: course.total_lessons\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Lessons\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border-end\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-primary\",\n                    children: [course.duration, \"h\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Duration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border-end\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-primary\",\n                    children: course.total_students\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Students\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"text-primary\",\n                  children: \"Beginner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: \"Level\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), is_enrolled ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"alert alert-success\",\n              role: \"alert\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-check-circle me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this), \"You are enrolled in this course!\", enrollment && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ms-2\",\n                children: [\"Progress: \", enrollment.progress, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-grid gap-2 d-md-flex\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-primary btn-lg me-md-2\",\n                onClick: handleEnroll,\n                disabled: enrolling,\n                children: enrolling ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"spinner-border spinner-border-sm me-2\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 25\n                  }, this), \"Enrolling...\"]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-play me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 25\n                  }, this), course.price === 0 ? 'Enroll for Free' : `Enroll for ${formatPrice(course.price)}`]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-outline-secondary btn-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-heart me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 21\n                }, this), \"Add to Wishlist\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), can_access_content && course.lessons && course.lessons.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"Course Content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"accordion\",\n              id: \"lessonsAccordion\",\n              children: course.lessons.map((lesson, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"accordion-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"accordion-header\",\n                  id: `heading${lesson.id}`,\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"accordion-button collapsed\",\n                    type: \"button\",\n                    \"data-bs-toggle\": \"collapse\",\n                    \"data-bs-target\": `#collapse${lesson.id}`,\n                    \"aria-expanded\": \"false\",\n                    \"aria-controls\": `collapse${lesson.id}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"me-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: `fas ${lesson.lesson_type === 'video' ? 'fa-play-circle' : 'fa-file-text'} text-primary`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 256,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 255,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-grow-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"fw-medium\",\n                        children: lesson.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 259,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"text-muted\",\n                        children: [lesson.duration_minutes, \" minutes\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 260,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 258,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  id: `collapse${lesson.id}`,\n                  className: \"accordion-collapse collapse\",\n                  \"aria-labelledby\": `heading${lesson.id}`,\n                  \"data-bs-parent\": \"#lessonsAccordion\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"accordion-body\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      children: lesson.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 27\n                    }, this), is_enrolled && /*#__PURE__*/_jsxDEV(Link, {\n                      to: `/courses/${courseId}/lessons/${lesson.id}`,\n                      className: \"btn btn-sm btn-primary\",\n                      children: \"Start Lesson\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 273,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 23\n                }, this)]\n              }, lesson.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this), can_access_content && course.materials && course.materials.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"Course Materials\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"list-group list-group-flush\",\n              children: course.materials.map(material => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"list-group-item d-flex justify-content-between align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: `fas ${material.file_type === 'pdf' ? 'fa-file-pdf' : 'fa-file'} text-danger me-2`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: material.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-0 text-muted small\",\n                    children: material.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 23\n                }, this), material.file && /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: material.file,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"btn btn-sm btn-outline-primary\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-download me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 27\n                  }, this), \"Download\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 25\n                }, this)]\n              }, material.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-lg-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"Instructor\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body text-center\",\n            children: [getUserAvatar(course.instructor, 80), /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"mt-3 mb-1\",\n              children: (_course$instructor = course.instructor) !== null && _course$instructor !== void 0 && _course$instructor.first_name && (_course$instructor2 = course.instructor) !== null && _course$instructor2 !== void 0 && _course$instructor2.last_name ? `${course.instructor.first_name} ${course.instructor.last_name}` : (_course$instructor3 = course.instructor) === null || _course$instructor3 === void 0 ? void 0 : _course$instructor3.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted small\",\n              children: \"Course Instructor\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                  className: \"text-primary\",\n                  children: \"4.8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: \"Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                  className: \"text-primary\",\n                  children: \"1,234\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: \"Students\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"This Course Includes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"list-unstyled\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-video text-primary me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 19\n                }, this), course.total_lessons, \" video lessons\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-clock text-primary me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 19\n                }, this), course.duration, \" hours of content\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-file-download text-primary me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 19\n                }, this), \"Downloadable resources\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-mobile-alt text-primary me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 19\n                }, this), \"Access on mobile and desktop\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-certificate text-primary me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 19\n                }, this), \"Certificate of completion\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-infinity text-primary me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this), \"Lifetime access\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"Related Courses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"More courses from this category coming soon!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n};\n_s(CourseDetail, \"s2uMT0v+NXHsEsTobceNdQgNM74=\", false, function () {\n  return [useParams, useAuth, useNavigate];\n});\n_c = CourseDetail;\nexport default CourseDetail;\nvar _c;\n$RefreshReg$(_c, \"CourseDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "useNavigate", "courseAPI", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CourseDetail", "_s", "_course$category2", "_course$instructor", "_course$instructor2", "_course$instructor3", "courseId", "user", "isAuthenticated", "navigate", "courseData", "setCourseData", "loading", "setLoading", "enrolling", "setEnrolling", "error", "setError", "loadCourseDetail", "response", "getCourseDetail", "data", "console", "handleEnroll", "role", "alert", "enrollCourse", "message", "_error$response", "_error$response$data", "errorMessage", "formatPrice", "price", "getUserAvatar", "size", "initials", "first_name", "last_name", "toUpperCase", "username", "className", "style", "width", "height", "fontSize", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getCourseImage", "course", "_course$category", "banner", "categoryImages", "category", "title", "to", "is_enrolled", "enrollment", "can_access_content", "src", "alt", "objectFit", "map", "star", "description", "total_lessons", "duration", "total_students", "progress", "onClick", "disabled", "lessons", "length", "id", "lesson", "index", "type", "lesson_type", "duration_minutes", "materials", "material", "file_type", "file", "href", "target", "rel", "instructor", "_c", "$RefreshReg$"], "sources": ["E:/Downloads/lms_backend/frontend/src/components/courses/CourseDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';\nimport { courseAPI } from '../../services/api';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst CourseDetail = () => {\n  const { courseId } = useParams();\n  const { user, isAuthenticated } = useAuth();\n  const navigate = useNavigate();\n  const [courseData, setCourseData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [enrolling, setEnrolling] = useState(false);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    loadCourseDetail();\n  }, [courseId]);\n\n  const loadCourseDetail = async () => {\n    try {\n      setLoading(true);\n      const response = await courseAPI.getCourseDetail(courseId);\n      setCourseData(response.data);\n    } catch (error) {\n      console.error('Failed to load course detail:', error);\n      setError('Failed to load course details. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleEnroll = async () => {\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n\n    if (user?.role !== 'student') {\n      alert('Only students can enroll in courses');\n      return;\n    }\n\n    try {\n      setEnrolling(true);\n      const response = await courseAPI.enrollCourse(courseId);\n      alert(response.data.message);\n      // Reload course data to update enrollment status\n      loadCourseDetail();\n    } catch (error) {\n      const errorMessage = error.response?.data?.error || 'Failed to enroll in course';\n      alert(errorMessage);\n    } finally {\n      setEnrolling(false);\n    }\n  };\n\n  const formatPrice = (price) => {\n    return price === 0 ? 'Free' : `$${price}`;\n  };\n\n  const getUserAvatar = (user, size = 48) => {\n    if (!user) return null;\n\n    const initials = user.first_name && user.last_name\n      ? `${user.first_name[0]}${user.last_name[0]}`.toUpperCase()\n      : user.username[0].toUpperCase();\n\n    return (\n      <div\n        className=\"user-avatar d-flex align-items-center justify-content-center text-white rounded-circle\"\n        style={{ width: size, height: size, fontSize: size * 0.4 }}\n      >\n        {initials}\n      </div>\n    );\n  };\n\n  const getCourseImage = (course) => {\n    if (course.banner) {\n      return course.banner;\n    }\n\n    // Fallback images based on category\n    const categoryImages = {\n      'Programming': 'https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&h=400&q=80',\n      'Web Development': 'https://images.unsplash.com/photo-1547658719-da2b51169166?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&h=400&q=80',\n      'Data Science': 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&h=400&q=80',\n      'Mobile Development': 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&h=400&q=80',\n      'Machine Learning': 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&h=400&q=80'\n    };\n\n    return categoryImages[course.category?.title] || 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&h=400&q=80';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"container my-5\">\n        <div className=\"d-flex justify-content-center align-items-center\" style={{ height: '50vh' }}>\n          <div className=\"spinner-border text-primary\" role=\"status\">\n            <span className=\"visually-hidden\">Loading...</span>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"container my-5\">\n        <div className=\"alert alert-danger\" role=\"alert\">\n          <h4 className=\"alert-heading\">Error</h4>\n          <p>{error}</p>\n          <hr />\n          <Link to=\"/courses\" className=\"btn btn-primary\">Back to Courses</Link>\n        </div>\n      </div>\n    );\n  }\n\n  if (!courseData) {\n    return (\n      <div className=\"container my-5\">\n        <div className=\"alert alert-warning\" role=\"alert\">\n          Course not found.\n          <Link to=\"/courses\" className=\"btn btn-primary ms-3\">Back to Courses</Link>\n        </div>\n      </div>\n    );\n  }\n\n  const { course, is_enrolled, enrollment, can_access_content } = courseData;\n\n  return (\n    <div className=\"container my-5\">\n      {/* Breadcrumb */}\n      <nav aria-label=\"breadcrumb\" className=\"mb-4\">\n        <ol className=\"breadcrumb\">\n          <li className=\"breadcrumb-item\"><Link to=\"/\">Home</Link></li>\n          <li className=\"breadcrumb-item\"><Link to=\"/courses\">Courses</Link></li>\n          <li className=\"breadcrumb-item active\" aria-current=\"page\">{course.title}</li>\n        </ol>\n      </nav>\n\n      <div className=\"row\">\n        {/* Main Content */}\n        <div className=\"col-lg-8\">\n          {/* Course Header */}\n          <div className=\"card mb-4\">\n            <img\n              src={getCourseImage(course)}\n              className=\"card-img-top\"\n              alt={course.title}\n              style={{ height: '300px', objectFit: 'cover' }}\n            />\n            <div className=\"card-body\">\n              <div className=\"d-flex justify-content-between align-items-start mb-3\">\n                <div>\n                  <span className=\"badge badge-category mb-2\">{course.category?.title}</span>\n                  <h1 className=\"card-title h3\">{course.title}</h1>\n                </div>\n                <div className=\"text-end\">\n                  <div className=\"rating mb-2\">\n                    {[1,2,3,4,5].map(star => (\n                      <i key={star} className=\"fas fa-star text-warning\"></i>\n                    ))}\n                    <span className=\"ms-1 text-dark\">5.0 (128 reviews)</span>\n                  </div>\n                  <p className=\"h4 text-primary mb-0\">{formatPrice(course.price)}</p>\n                </div>\n              </div>\n\n              <p className=\"card-text\">{course.description}</p>\n\n              {/* Course Stats */}\n              <div className=\"row text-center mb-4\">\n                <div className=\"col-md-3\">\n                  <div className=\"border-end\">\n                    <h5 className=\"text-primary\">{course.total_lessons}</h5>\n                    <small className=\"text-muted\">Lessons</small>\n                  </div>\n                </div>\n                <div className=\"col-md-3\">\n                  <div className=\"border-end\">\n                    <h5 className=\"text-primary\">{course.duration}h</h5>\n                    <small className=\"text-muted\">Duration</small>\n                  </div>\n                </div>\n                <div className=\"col-md-3\">\n                  <div className=\"border-end\">\n                    <h5 className=\"text-primary\">{course.total_students}</h5>\n                    <small className=\"text-muted\">Students</small>\n                  </div>\n                </div>\n                <div className=\"col-md-3\">\n                  <h5 className=\"text-primary\">Beginner</h5>\n                  <small className=\"text-muted\">Level</small>\n                </div>\n              </div>\n\n              {/* Enrollment Status */}\n              {is_enrolled ? (\n                <div className=\"alert alert-success\" role=\"alert\">\n                  <i className=\"fas fa-check-circle me-2\"></i>\n                  You are enrolled in this course! \n                  {enrollment && (\n                    <span className=\"ms-2\">Progress: {enrollment.progress}%</span>\n                  )}\n                </div>\n              ) : (\n                <div className=\"d-grid gap-2 d-md-flex\">\n                  <button \n                    className=\"btn btn-primary btn-lg me-md-2\" \n                    onClick={handleEnroll}\n                    disabled={enrolling}\n                  >\n                    {enrolling ? (\n                      <>\n                        <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                        Enrolling...\n                      </>\n                    ) : (\n                      <>\n                        <i className=\"fas fa-play me-2\"></i>\n                        {course.price === 0 ? 'Enroll for Free' : `Enroll for ${formatPrice(course.price)}`}\n                      </>\n                    )}\n                  </button>\n                  <button className=\"btn btn-outline-secondary btn-lg\">\n                    <i className=\"fas fa-heart me-2\"></i>Add to Wishlist\n                  </button>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Course Content */}\n          {can_access_content && course.lessons && course.lessons.length > 0 && (\n            <div className=\"card mb-4\">\n              <div className=\"card-header\">\n                <h5 className=\"mb-0\">Course Content</h5>\n              </div>\n              <div className=\"card-body\">\n                <div className=\"accordion\" id=\"lessonsAccordion\">\n                  {course.lessons.map((lesson, index) => (\n                    <div key={lesson.id} className=\"accordion-item\">\n                      <h2 className=\"accordion-header\" id={`heading${lesson.id}`}>\n                        <button \n                          className=\"accordion-button collapsed\" \n                          type=\"button\" \n                          data-bs-toggle=\"collapse\" \n                          data-bs-target={`#collapse${lesson.id}`}\n                          aria-expanded=\"false\" \n                          aria-controls={`collapse${lesson.id}`}\n                        >\n                          <span className=\"me-3\">\n                            <i className={`fas ${lesson.lesson_type === 'video' ? 'fa-play-circle' : 'fa-file-text'} text-primary`}></i>\n                          </span>\n                          <div className=\"flex-grow-1\">\n                            <div className=\"fw-medium\">{lesson.title}</div>\n                            <small className=\"text-muted\">{lesson.duration_minutes} minutes</small>\n                          </div>\n                        </button>\n                      </h2>\n                      <div \n                        id={`collapse${lesson.id}`} \n                        className=\"accordion-collapse collapse\" \n                        aria-labelledby={`heading${lesson.id}`}\n                        data-bs-parent=\"#lessonsAccordion\"\n                      >\n                        <div className=\"accordion-body\">\n                          <p>{lesson.description}</p>\n                          {is_enrolled && (\n                            <Link \n                              to={`/courses/${courseId}/lessons/${lesson.id}`}\n                              className=\"btn btn-sm btn-primary\"\n                            >\n                              Start Lesson\n                            </Link>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Course Materials */}\n          {can_access_content && course.materials && course.materials.length > 0 && (\n            <div className=\"card mb-4\">\n              <div className=\"card-header\">\n                <h5 className=\"mb-0\">Course Materials</h5>\n              </div>\n              <div className=\"card-body\">\n                <div className=\"list-group list-group-flush\">\n                  {course.materials.map(material => (\n                    <div key={material.id} className=\"list-group-item d-flex justify-content-between align-items-center\">\n                      <div>\n                        <i className={`fas ${material.file_type === 'pdf' ? 'fa-file-pdf' : 'fa-file'} text-danger me-2`}></i>\n                        <strong>{material.title}</strong>\n                        <p className=\"mb-0 text-muted small\">{material.description}</p>\n                      </div>\n                      {material.file && (\n                        <a href={material.file} target=\"_blank\" rel=\"noopener noreferrer\" className=\"btn btn-sm btn-outline-primary\">\n                          <i className=\"fas fa-download me-1\"></i>Download\n                        </a>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Sidebar */}\n        <div className=\"col-lg-4\">\n          {/* Instructor Info */}\n          <div className=\"card mb-4\">\n            <div className=\"card-header\">\n              <h5 className=\"mb-0\">Instructor</h5>\n            </div>\n            <div className=\"card-body text-center\">\n              {getUserAvatar(course.instructor, 80)}\n              <h6 className=\"mt-3 mb-1\">\n                {course.instructor?.first_name && course.instructor?.last_name \n                  ? `${course.instructor.first_name} ${course.instructor.last_name}`\n                  : course.instructor?.username}\n              </h6>\n              <p className=\"text-muted small\">Course Instructor</p>\n              <div className=\"row text-center\">\n                <div className=\"col-6\">\n                  <h6 className=\"text-primary\">4.8</h6>\n                  <small className=\"text-muted\">Rating</small>\n                </div>\n                <div className=\"col-6\">\n                  <h6 className=\"text-primary\">1,234</h6>\n                  <small className=\"text-muted\">Students</small>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Course Features */}\n          <div className=\"card mb-4\">\n            <div className=\"card-header\">\n              <h5 className=\"mb-0\">This Course Includes</h5>\n            </div>\n            <div className=\"card-body\">\n              <ul className=\"list-unstyled\">\n                <li className=\"mb-2\">\n                  <i className=\"fas fa-video text-primary me-2\"></i>\n                  {course.total_lessons} video lessons\n                </li>\n                <li className=\"mb-2\">\n                  <i className=\"fas fa-clock text-primary me-2\"></i>\n                  {course.duration} hours of content\n                </li>\n                <li className=\"mb-2\">\n                  <i className=\"fas fa-file-download text-primary me-2\"></i>\n                  Downloadable resources\n                </li>\n                <li className=\"mb-2\">\n                  <i className=\"fas fa-mobile-alt text-primary me-2\"></i>\n                  Access on mobile and desktop\n                </li>\n                <li className=\"mb-2\">\n                  <i className=\"fas fa-certificate text-primary me-2\"></i>\n                  Certificate of completion\n                </li>\n                <li className=\"mb-2\">\n                  <i className=\"fas fa-infinity text-primary me-2\"></i>\n                  Lifetime access\n                </li>\n              </ul>\n            </div>\n          </div>\n\n          {/* Related Courses */}\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h5 className=\"mb-0\">Related Courses</h5>\n            </div>\n            <div className=\"card-body\">\n              <p className=\"text-muted\">More courses from this category coming soon!</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CourseDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,iBAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA;EACzB,MAAM;IAAEC;EAAS,CAAC,GAAGf,SAAS,CAAC,CAAC;EAChC,MAAM;IAAEgB,IAAI;IAAEC;EAAgB,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC3C,MAAMc,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd4B,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACZ,QAAQ,CAAC,CAAC;EAEd,MAAMY,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAMzB,SAAS,CAAC0B,eAAe,CAACd,QAAQ,CAAC;MAC1DK,aAAa,CAACQ,QAAQ,CAACE,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDC,QAAQ,CAAC,kDAAkD,CAAC;IAC9D,CAAC,SAAS;MACRJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACf,eAAe,EAAE;MACpBC,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEA,IAAI,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,IAAI,MAAK,SAAS,EAAE;MAC5BC,KAAK,CAAC,qCAAqC,CAAC;MAC5C;IACF;IAEA,IAAI;MACFV,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMI,QAAQ,GAAG,MAAMzB,SAAS,CAACgC,YAAY,CAACpB,QAAQ,CAAC;MACvDmB,KAAK,CAACN,QAAQ,CAACE,IAAI,CAACM,OAAO,CAAC;MAC5B;MACAT,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOF,KAAK,EAAE;MAAA,IAAAY,eAAA,EAAAC,oBAAA;MACd,MAAMC,YAAY,GAAG,EAAAF,eAAA,GAAAZ,KAAK,CAACG,QAAQ,cAAAS,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBP,IAAI,cAAAQ,oBAAA,uBAApBA,oBAAA,CAAsBb,KAAK,KAAI,4BAA4B;MAChFS,KAAK,CAACK,YAAY,CAAC;IACrB,CAAC,SAAS;MACRf,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMgB,WAAW,GAAIC,KAAK,IAAK;IAC7B,OAAOA,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG,IAAIA,KAAK,EAAE;EAC3C,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAC1B,IAAI,EAAE2B,IAAI,GAAG,EAAE,KAAK;IACzC,IAAI,CAAC3B,IAAI,EAAE,OAAO,IAAI;IAEtB,MAAM4B,QAAQ,GAAG5B,IAAI,CAAC6B,UAAU,IAAI7B,IAAI,CAAC8B,SAAS,GAC9C,GAAG9B,IAAI,CAAC6B,UAAU,CAAC,CAAC,CAAC,GAAG7B,IAAI,CAAC8B,SAAS,CAAC,CAAC,CAAC,EAAE,CAACC,WAAW,CAAC,CAAC,GACzD/B,IAAI,CAACgC,QAAQ,CAAC,CAAC,CAAC,CAACD,WAAW,CAAC,CAAC;IAElC,oBACEzC,OAAA;MACE2C,SAAS,EAAC,wFAAwF;MAClGC,KAAK,EAAE;QAAEC,KAAK,EAAER,IAAI;QAAES,MAAM,EAAET,IAAI;QAAEU,QAAQ,EAAEV,IAAI,GAAG;MAAI,CAAE;MAAAW,QAAA,EAE1DV;IAAQ;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IAAA,IAAAC,gBAAA;IACjC,IAAID,MAAM,CAACE,MAAM,EAAE;MACjB,OAAOF,MAAM,CAACE,MAAM;IACtB;;IAEA;IACA,MAAMC,cAAc,GAAG;MACrB,aAAa,EAAE,mHAAmH;MAClI,iBAAiB,EAAE,gHAAgH;MACnI,cAAc,EAAE,gHAAgH;MAChI,oBAAoB,EAAE,mHAAmH;MACzI,kBAAkB,EAAE;IACtB,CAAC;IAED,OAAOA,cAAc,EAAAF,gBAAA,GAACD,MAAM,CAACI,QAAQ,cAAAH,gBAAA,uBAAfA,gBAAA,CAAiBI,KAAK,CAAC,IAAI,mHAAmH;EACtK,CAAC;EAED,IAAI5C,OAAO,EAAE;IACX,oBACEf,OAAA;MAAK2C,SAAS,EAAC,gBAAgB;MAAAK,QAAA,eAC7BhD,OAAA;QAAK2C,SAAS,EAAC,kDAAkD;QAACC,KAAK,EAAE;UAAEE,MAAM,EAAE;QAAO,CAAE;QAAAE,QAAA,eAC1FhD,OAAA;UAAK2C,SAAS,EAAC,6BAA6B;UAAChB,IAAI,EAAC,QAAQ;UAAAqB,QAAA,eACxDhD,OAAA;YAAM2C,SAAS,EAAC,iBAAiB;YAAAK,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIjC,KAAK,EAAE;IACT,oBACEnB,OAAA;MAAK2C,SAAS,EAAC,gBAAgB;MAAAK,QAAA,eAC7BhD,OAAA;QAAK2C,SAAS,EAAC,oBAAoB;QAAChB,IAAI,EAAC,OAAO;QAAAqB,QAAA,gBAC9ChD,OAAA;UAAI2C,SAAS,EAAC,eAAe;UAAAK,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxCpD,OAAA;UAAAgD,QAAA,EAAI7B;QAAK;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdpD,OAAA;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNpD,OAAA,CAACL,IAAI;UAACiE,EAAE,EAAC,UAAU;UAACjB,SAAS,EAAC,iBAAiB;UAAAK,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACvC,UAAU,EAAE;IACf,oBACEb,OAAA;MAAK2C,SAAS,EAAC,gBAAgB;MAAAK,QAAA,eAC7BhD,OAAA;QAAK2C,SAAS,EAAC,qBAAqB;QAAChB,IAAI,EAAC,OAAO;QAAAqB,QAAA,GAAC,mBAEhD,eAAAhD,OAAA,CAACL,IAAI;UAACiE,EAAE,EAAC,UAAU;UAACjB,SAAS,EAAC,sBAAsB;UAAAK,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAM;IAAEE,MAAM;IAAEO,WAAW;IAAEC,UAAU;IAAEC;EAAmB,CAAC,GAAGlD,UAAU;EAE1E,oBACEb,OAAA;IAAK2C,SAAS,EAAC,gBAAgB;IAAAK,QAAA,gBAE7BhD,OAAA;MAAK,cAAW,YAAY;MAAC2C,SAAS,EAAC,MAAM;MAAAK,QAAA,eAC3ChD,OAAA;QAAI2C,SAAS,EAAC,YAAY;QAAAK,QAAA,gBACxBhD,OAAA;UAAI2C,SAAS,EAAC,iBAAiB;UAAAK,QAAA,eAAChD,OAAA,CAACL,IAAI;YAACiE,EAAE,EAAC,GAAG;YAAAZ,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7DpD,OAAA;UAAI2C,SAAS,EAAC,iBAAiB;UAAAK,QAAA,eAAChD,OAAA,CAACL,IAAI;YAACiE,EAAE,EAAC,UAAU;YAAAZ,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvEpD,OAAA;UAAI2C,SAAS,EAAC,wBAAwB;UAAC,gBAAa,MAAM;UAAAK,QAAA,EAAEM,MAAM,CAACK;QAAK;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAENpD,OAAA;MAAK2C,SAAS,EAAC,KAAK;MAAAK,QAAA,gBAElBhD,OAAA;QAAK2C,SAAS,EAAC,UAAU;QAAAK,QAAA,gBAEvBhD,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAK,QAAA,gBACxBhD,OAAA;YACEgE,GAAG,EAAEX,cAAc,CAACC,MAAM,CAAE;YAC5BX,SAAS,EAAC,cAAc;YACxBsB,GAAG,EAAEX,MAAM,CAACK,KAAM;YAClBf,KAAK,EAAE;cAAEE,MAAM,EAAE,OAAO;cAAEoB,SAAS,EAAE;YAAQ;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACFpD,OAAA;YAAK2C,SAAS,EAAC,WAAW;YAAAK,QAAA,gBACxBhD,OAAA;cAAK2C,SAAS,EAAC,uDAAuD;cAAAK,QAAA,gBACpEhD,OAAA;gBAAAgD,QAAA,gBACEhD,OAAA;kBAAM2C,SAAS,EAAC,2BAA2B;kBAAAK,QAAA,GAAA3C,iBAAA,GAAEiD,MAAM,CAACI,QAAQ,cAAArD,iBAAA,uBAAfA,iBAAA,CAAiBsD;gBAAK;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3EpD,OAAA;kBAAI2C,SAAS,EAAC,eAAe;kBAAAK,QAAA,EAAEM,MAAM,CAACK;gBAAK;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACNpD,OAAA;gBAAK2C,SAAS,EAAC,UAAU;gBAAAK,QAAA,gBACvBhD,OAAA;kBAAK2C,SAAS,EAAC,aAAa;kBAAAK,QAAA,GACzB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAACmB,GAAG,CAACC,IAAI,iBACnBpE,OAAA;oBAAc2C,SAAS,EAAC;kBAA0B,GAA1CyB,IAAI;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA0C,CACvD,CAAC,eACFpD,OAAA;oBAAM2C,SAAS,EAAC,gBAAgB;oBAAAK,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNpD,OAAA;kBAAG2C,SAAS,EAAC,sBAAsB;kBAAAK,QAAA,EAAEd,WAAW,CAACoB,MAAM,CAACnB,KAAK;gBAAC;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpD,OAAA;cAAG2C,SAAS,EAAC,WAAW;cAAAK,QAAA,EAAEM,MAAM,CAACe;YAAW;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAGjDpD,OAAA;cAAK2C,SAAS,EAAC,sBAAsB;cAAAK,QAAA,gBACnChD,OAAA;gBAAK2C,SAAS,EAAC,UAAU;gBAAAK,QAAA,eACvBhD,OAAA;kBAAK2C,SAAS,EAAC,YAAY;kBAAAK,QAAA,gBACzBhD,OAAA;oBAAI2C,SAAS,EAAC,cAAc;oBAAAK,QAAA,EAAEM,MAAM,CAACgB;kBAAa;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxDpD,OAAA;oBAAO2C,SAAS,EAAC,YAAY;oBAAAK,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpD,OAAA;gBAAK2C,SAAS,EAAC,UAAU;gBAAAK,QAAA,eACvBhD,OAAA;kBAAK2C,SAAS,EAAC,YAAY;kBAAAK,QAAA,gBACzBhD,OAAA;oBAAI2C,SAAS,EAAC,cAAc;oBAAAK,QAAA,GAAEM,MAAM,CAACiB,QAAQ,EAAC,GAAC;kBAAA;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpDpD,OAAA;oBAAO2C,SAAS,EAAC,YAAY;oBAAAK,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpD,OAAA;gBAAK2C,SAAS,EAAC,UAAU;gBAAAK,QAAA,eACvBhD,OAAA;kBAAK2C,SAAS,EAAC,YAAY;kBAAAK,QAAA,gBACzBhD,OAAA;oBAAI2C,SAAS,EAAC,cAAc;oBAAAK,QAAA,EAAEM,MAAM,CAACkB;kBAAc;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzDpD,OAAA;oBAAO2C,SAAS,EAAC,YAAY;oBAAAK,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpD,OAAA;gBAAK2C,SAAS,EAAC,UAAU;gBAAAK,QAAA,gBACvBhD,OAAA;kBAAI2C,SAAS,EAAC,cAAc;kBAAAK,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1CpD,OAAA;kBAAO2C,SAAS,EAAC,YAAY;kBAAAK,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLS,WAAW,gBACV7D,OAAA;cAAK2C,SAAS,EAAC,qBAAqB;cAAChB,IAAI,EAAC,OAAO;cAAAqB,QAAA,gBAC/ChD,OAAA;gBAAG2C,SAAS,EAAC;cAA0B;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,oCAE5C,EAACU,UAAU,iBACT9D,OAAA;gBAAM2C,SAAS,EAAC,MAAM;gBAAAK,QAAA,GAAC,YAAU,EAACc,UAAU,CAACW,QAAQ,EAAC,GAAC;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAC9D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,gBAENpD,OAAA;cAAK2C,SAAS,EAAC,wBAAwB;cAAAK,QAAA,gBACrChD,OAAA;gBACE2C,SAAS,EAAC,gCAAgC;gBAC1C+B,OAAO,EAAEhD,YAAa;gBACtBiD,QAAQ,EAAE1D,SAAU;gBAAA+B,QAAA,EAEnB/B,SAAS,gBACRjB,OAAA,CAAAE,SAAA;kBAAA8C,QAAA,gBACEhD,OAAA;oBAAM2C,SAAS,EAAC,uCAAuC;oBAAChB,IAAI,EAAC,QAAQ;oBAAC,eAAY;kBAAM;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,gBAElG;gBAAA,eAAE,CAAC,gBAEHpD,OAAA,CAAAE,SAAA;kBAAA8C,QAAA,gBACEhD,OAAA;oBAAG2C,SAAS,EAAC;kBAAkB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACnCE,MAAM,CAACnB,KAAK,KAAK,CAAC,GAAG,iBAAiB,GAAG,cAAcD,WAAW,CAACoB,MAAM,CAACnB,KAAK,CAAC,EAAE;gBAAA,eACnF;cACH;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eACTpD,OAAA;gBAAQ2C,SAAS,EAAC,kCAAkC;gBAAAK,QAAA,gBAClDhD,OAAA;kBAAG2C,SAAS,EAAC;gBAAmB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,mBACvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLW,kBAAkB,IAAIT,MAAM,CAACsB,OAAO,IAAItB,MAAM,CAACsB,OAAO,CAACC,MAAM,GAAG,CAAC,iBAChE7E,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAK,QAAA,gBACxBhD,OAAA;YAAK2C,SAAS,EAAC,aAAa;YAAAK,QAAA,eAC1BhD,OAAA;cAAI2C,SAAS,EAAC,MAAM;cAAAK,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACNpD,OAAA;YAAK2C,SAAS,EAAC,WAAW;YAAAK,QAAA,eACxBhD,OAAA;cAAK2C,SAAS,EAAC,WAAW;cAACmC,EAAE,EAAC,kBAAkB;cAAA9B,QAAA,EAC7CM,MAAM,CAACsB,OAAO,CAACT,GAAG,CAAC,CAACY,MAAM,EAAEC,KAAK,kBAChChF,OAAA;gBAAqB2C,SAAS,EAAC,gBAAgB;gBAAAK,QAAA,gBAC7ChD,OAAA;kBAAI2C,SAAS,EAAC,kBAAkB;kBAACmC,EAAE,EAAE,UAAUC,MAAM,CAACD,EAAE,EAAG;kBAAA9B,QAAA,eACzDhD,OAAA;oBACE2C,SAAS,EAAC,4BAA4B;oBACtCsC,IAAI,EAAC,QAAQ;oBACb,kBAAe,UAAU;oBACzB,kBAAgB,YAAYF,MAAM,CAACD,EAAE,EAAG;oBACxC,iBAAc,OAAO;oBACrB,iBAAe,WAAWC,MAAM,CAACD,EAAE,EAAG;oBAAA9B,QAAA,gBAEtChD,OAAA;sBAAM2C,SAAS,EAAC,MAAM;sBAAAK,QAAA,eACpBhD,OAAA;wBAAG2C,SAAS,EAAE,OAAOoC,MAAM,CAACG,WAAW,KAAK,OAAO,GAAG,gBAAgB,GAAG,cAAc;sBAAgB;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxG,CAAC,eACPpD,OAAA;sBAAK2C,SAAS,EAAC,aAAa;sBAAAK,QAAA,gBAC1BhD,OAAA;wBAAK2C,SAAS,EAAC,WAAW;wBAAAK,QAAA,EAAE+B,MAAM,CAACpB;sBAAK;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/CpD,OAAA;wBAAO2C,SAAS,EAAC,YAAY;wBAAAK,QAAA,GAAE+B,MAAM,CAACI,gBAAgB,EAAC,UAAQ;sBAAA;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACLpD,OAAA;kBACE8E,EAAE,EAAE,WAAWC,MAAM,CAACD,EAAE,EAAG;kBAC3BnC,SAAS,EAAC,6BAA6B;kBACvC,mBAAiB,UAAUoC,MAAM,CAACD,EAAE,EAAG;kBACvC,kBAAe,mBAAmB;kBAAA9B,QAAA,eAElChD,OAAA;oBAAK2C,SAAS,EAAC,gBAAgB;oBAAAK,QAAA,gBAC7BhD,OAAA;sBAAAgD,QAAA,EAAI+B,MAAM,CAACV;oBAAW;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAC1BS,WAAW,iBACV7D,OAAA,CAACL,IAAI;sBACHiE,EAAE,EAAE,YAAYnD,QAAQ,YAAYsE,MAAM,CAACD,EAAE,EAAG;sBAChDnC,SAAS,EAAC,wBAAwB;sBAAAK,QAAA,EACnC;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GApCE2B,MAAM,CAACD,EAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqCd,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGAW,kBAAkB,IAAIT,MAAM,CAAC8B,SAAS,IAAI9B,MAAM,CAAC8B,SAAS,CAACP,MAAM,GAAG,CAAC,iBACpE7E,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAK,QAAA,gBACxBhD,OAAA;YAAK2C,SAAS,EAAC,aAAa;YAAAK,QAAA,eAC1BhD,OAAA;cAAI2C,SAAS,EAAC,MAAM;cAAAK,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACNpD,OAAA;YAAK2C,SAAS,EAAC,WAAW;YAAAK,QAAA,eACxBhD,OAAA;cAAK2C,SAAS,EAAC,6BAA6B;cAAAK,QAAA,EACzCM,MAAM,CAAC8B,SAAS,CAACjB,GAAG,CAACkB,QAAQ,iBAC5BrF,OAAA;gBAAuB2C,SAAS,EAAC,mEAAmE;gBAAAK,QAAA,gBAClGhD,OAAA;kBAAAgD,QAAA,gBACEhD,OAAA;oBAAG2C,SAAS,EAAE,OAAO0C,QAAQ,CAACC,SAAS,KAAK,KAAK,GAAG,aAAa,GAAG,SAAS;kBAAoB;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtGpD,OAAA;oBAAAgD,QAAA,EAASqC,QAAQ,CAAC1B;kBAAK;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eACjCpD,OAAA;oBAAG2C,SAAS,EAAC,uBAAuB;oBAAAK,QAAA,EAAEqC,QAAQ,CAAChB;kBAAW;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC,EACLiC,QAAQ,CAACE,IAAI,iBACZvF,OAAA;kBAAGwF,IAAI,EAAEH,QAAQ,CAACE,IAAK;kBAACE,MAAM,EAAC,QAAQ;kBAACC,GAAG,EAAC,qBAAqB;kBAAC/C,SAAS,EAAC,gCAAgC;kBAAAK,QAAA,gBAC1GhD,OAAA;oBAAG2C,SAAS,EAAC;kBAAsB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,YAC1C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACJ;cAAA,GAVOiC,QAAQ,CAACP,EAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWhB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNpD,OAAA;QAAK2C,SAAS,EAAC,UAAU;QAAAK,QAAA,gBAEvBhD,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAK,QAAA,gBACxBhD,OAAA;YAAK2C,SAAS,EAAC,aAAa;YAAAK,QAAA,eAC1BhD,OAAA;cAAI2C,SAAS,EAAC,MAAM;cAAAK,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNpD,OAAA;YAAK2C,SAAS,EAAC,uBAAuB;YAAAK,QAAA,GACnCZ,aAAa,CAACkB,MAAM,CAACqC,UAAU,EAAE,EAAE,CAAC,eACrC3F,OAAA;cAAI2C,SAAS,EAAC,WAAW;cAAAK,QAAA,EACtB,CAAA1C,kBAAA,GAAAgD,MAAM,CAACqC,UAAU,cAAArF,kBAAA,eAAjBA,kBAAA,CAAmBiC,UAAU,KAAAhC,mBAAA,GAAI+C,MAAM,CAACqC,UAAU,cAAApF,mBAAA,eAAjBA,mBAAA,CAAmBiC,SAAS,GAC1D,GAAGc,MAAM,CAACqC,UAAU,CAACpD,UAAU,IAAIe,MAAM,CAACqC,UAAU,CAACnD,SAAS,EAAE,IAAAhC,mBAAA,GAChE8C,MAAM,CAACqC,UAAU,cAAAnF,mBAAA,uBAAjBA,mBAAA,CAAmBkC;YAAQ;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACLpD,OAAA;cAAG2C,SAAS,EAAC,kBAAkB;cAAAK,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrDpD,OAAA;cAAK2C,SAAS,EAAC,iBAAiB;cAAAK,QAAA,gBAC9BhD,OAAA;gBAAK2C,SAAS,EAAC,OAAO;gBAAAK,QAAA,gBACpBhD,OAAA;kBAAI2C,SAAS,EAAC,cAAc;kBAAAK,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrCpD,OAAA;kBAAO2C,SAAS,EAAC,YAAY;kBAAAK,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACNpD,OAAA;gBAAK2C,SAAS,EAAC,OAAO;gBAAAK,QAAA,gBACpBhD,OAAA;kBAAI2C,SAAS,EAAC,cAAc;kBAAAK,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvCpD,OAAA;kBAAO2C,SAAS,EAAC,YAAY;kBAAAK,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpD,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAK,QAAA,gBACxBhD,OAAA;YAAK2C,SAAS,EAAC,aAAa;YAAAK,QAAA,eAC1BhD,OAAA;cAAI2C,SAAS,EAAC,MAAM;cAAAK,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNpD,OAAA;YAAK2C,SAAS,EAAC,WAAW;YAAAK,QAAA,eACxBhD,OAAA;cAAI2C,SAAS,EAAC,eAAe;cAAAK,QAAA,gBAC3BhD,OAAA;gBAAI2C,SAAS,EAAC,MAAM;gBAAAK,QAAA,gBAClBhD,OAAA;kBAAG2C,SAAS,EAAC;gBAAgC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACjDE,MAAM,CAACgB,aAAa,EAAC,gBACxB;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLpD,OAAA;gBAAI2C,SAAS,EAAC,MAAM;gBAAAK,QAAA,gBAClBhD,OAAA;kBAAG2C,SAAS,EAAC;gBAAgC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACjDE,MAAM,CAACiB,QAAQ,EAAC,mBACnB;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLpD,OAAA;gBAAI2C,SAAS,EAAC,MAAM;gBAAAK,QAAA,gBAClBhD,OAAA;kBAAG2C,SAAS,EAAC;gBAAwC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,0BAE5D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLpD,OAAA;gBAAI2C,SAAS,EAAC,MAAM;gBAAAK,QAAA,gBAClBhD,OAAA;kBAAG2C,SAAS,EAAC;gBAAqC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,gCAEzD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLpD,OAAA;gBAAI2C,SAAS,EAAC,MAAM;gBAAAK,QAAA,gBAClBhD,OAAA;kBAAG2C,SAAS,EAAC;gBAAsC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,6BAE1D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLpD,OAAA;gBAAI2C,SAAS,EAAC,MAAM;gBAAAK,QAAA,gBAClBhD,OAAA;kBAAG2C,SAAS,EAAC;gBAAmC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,mBAEvD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpD,OAAA;UAAK2C,SAAS,EAAC,MAAM;UAAAK,QAAA,gBACnBhD,OAAA;YAAK2C,SAAS,EAAC,aAAa;YAAAK,QAAA,eAC1BhD,OAAA;cAAI2C,SAAS,EAAC,MAAM;cAAAK,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACNpD,OAAA;YAAK2C,SAAS,EAAC,WAAW;YAAAK,QAAA,eACxBhD,OAAA;cAAG2C,SAAS,EAAC,YAAY;cAAAK,QAAA,EAAC;YAA4C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChD,EAAA,CAnYID,YAAY;EAAA,QACKT,SAAS,EACII,OAAO,EACxBF,WAAW;AAAA;AAAAgG,EAAA,GAHxBzF,YAAY;AAqYlB,eAAeA,YAAY;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}