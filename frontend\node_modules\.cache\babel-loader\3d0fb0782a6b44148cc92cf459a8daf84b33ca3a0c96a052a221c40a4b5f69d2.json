{"ast": null, "code": "var _jsxFileName = \"E:\\\\Downloads\\\\lms_backend\\\\frontend\\\\src\\\\components\\\\common\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  requiredRole = null\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    user,\n    loading\n  } = useAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        height: '50vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Check role-based access if required\n  if (requiredRole && (user === null || user === void 0 ? void 0 : user.role) !== requiredRole) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container my-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        role: \"alert\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"alert-heading\",\n          children: \"Access Denied\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"You don't have permission to access this page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mb-0\",\n          children: [\"This page requires \", requiredRole, \" privileges. Your current role is: \", user === null || user === void 0 ? void 0 : user.role]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this);\n  }\n  return children;\n};\n_s(ProtectedRoute, \"zEGFxQ50Ze7Wo58QBItO0ZM7uxw=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "requiredRole", "_s", "isAuthenticated", "user", "loading", "location", "className", "style", "height", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "state", "from", "replace", "_c", "$RefreshReg$"], "sources": ["E:/Downloads/lms_backend/frontend/src/components/common/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst ProtectedRoute = ({ children, requiredRole = null }) => {\n  const { isAuthenticated, user, loading } = useAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <div className=\"d-flex justify-content-center align-items-center\" style={{ height: '50vh' }}>\n        <div className=\"spinner-border text-primary\" role=\"status\">\n          <span className=\"visually-hidden\">Loading...</span>\n        </div>\n      </div>\n    );\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  // Check role-based access if required\n  if (requiredRole && user?.role !== requiredRole) {\n    return (\n      <div className=\"container my-5\">\n        <div className=\"alert alert-danger\" role=\"alert\">\n          <h4 className=\"alert-heading\">Access Denied</h4>\n          <p>You don't have permission to access this page.</p>\n          <hr />\n          <p className=\"mb-0\">\n            This page requires {requiredRole} privileges. Your current role is: {user?.role}\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,YAAY,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC5D,MAAM;IAAEC,eAAe;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGT,OAAO,CAAC,CAAC;EACpD,MAAMU,QAAQ,GAAGX,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAIU,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKS,SAAS,EAAC,kDAAkD;MAACC,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAO,CAAE;MAAAT,QAAA,eAC1FF,OAAA;QAAKS,SAAS,EAAC,6BAA6B;QAACG,IAAI,EAAC,QAAQ;QAAAV,QAAA,eACxDF,OAAA;UAAMS,SAAS,EAAC,iBAAiB;UAAAP,QAAA,EAAC;QAAU;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAACX,eAAe,EAAE;IACpB,oBAAOL,OAAA,CAACJ,QAAQ;MAACqB,EAAE,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,IAAI,EAAEX;MAAS,CAAE;MAACY,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE;;EAEA;EACA,IAAIb,YAAY,IAAI,CAAAG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,IAAI,MAAKT,YAAY,EAAE;IAC/C,oBACEH,OAAA;MAAKS,SAAS,EAAC,gBAAgB;MAAAP,QAAA,eAC7BF,OAAA;QAAKS,SAAS,EAAC,oBAAoB;QAACG,IAAI,EAAC,OAAO;QAAAV,QAAA,gBAC9CF,OAAA;UAAIS,SAAS,EAAC,eAAe;UAAAP,QAAA,EAAC;QAAa;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChDhB,OAAA;UAAAE,QAAA,EAAG;QAA8C;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrDhB,OAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhB,OAAA;UAAGS,SAAS,EAAC,MAAM;UAAAP,QAAA,GAAC,qBACC,EAACC,YAAY,EAAC,qCAAmC,EAACG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,IAAI;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,OAAOd,QAAQ;AACjB,CAAC;AAACE,EAAA,CArCIH,cAAc;EAAA,QACyBH,OAAO,EACjCD,WAAW;AAAA;AAAAwB,EAAA,GAFxBpB,cAAc;AAuCpB,eAAeA,cAAc;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}