{"ast": null, "code": "var _jsxFileName = \"E:\\\\Downloads\\\\lms_backend\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // Check if user is authenticated on app load\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n  const checkAuthStatus = async () => {\n    try {\n      const response = await authAPI.getCurrentUser();\n      if (response.data.is_authenticated) {\n        setUser(response.data.user);\n        setIsAuthenticated(true);\n      }\n    } catch (error) {\n      console.error('Auth check failed:', error);\n      setUser(null);\n      setIsAuthenticated(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const login = async credentials => {\n    try {\n      const response = await authAPI.login(credentials);\n      setUser(response.data.user);\n      setIsAuthenticated(true);\n      return {\n        success: true,\n        message: response.data.message\n      };\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response$data$, _error$response2, _error$response2$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : (_error$response$data$ = _error$response$data.non_field_errors) === null || _error$response$data$ === void 0 ? void 0 : _error$response$data$[0]) || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || 'Login failed';\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      const response = await authAPI.register(userData);\n      setUser(response.data.user);\n      setIsAuthenticated(true);\n      return {\n        success: true,\n        message: response.data.message\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data, _error$response3$data2, _error$response4, _error$response4$data, _error$response5;\n      const errorMessage = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : (_error$response3$data2 = _error$response3$data.non_field_errors) === null || _error$response3$data2 === void 0 ? void 0 : _error$response3$data2[0]) || ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || 'Registration failed';\n      return {\n        success: false,\n        message: errorMessage,\n        errors: (_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.data\n      };\n    }\n  };\n  const logout = async () => {\n    try {\n      await authAPI.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setUser(null);\n      setIsAuthenticated(false);\n    }\n  };\n  const value = {\n    user,\n    isAuthenticated,\n    loading,\n    login,\n    register,\n    logout,\n    checkAuthStatus\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"xBgiRagNfQVCfEr2dT2PptfN+TE=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authAPI", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "isAuthenticated", "setIsAuthenticated", "checkAuthStatus", "response", "getCurrentUser", "data", "is_authenticated", "error", "console", "login", "credentials", "success", "message", "_error$response", "_error$response$data", "_error$response$data$", "_error$response2", "_error$response2$data", "errorMessage", "non_field_errors", "detail", "register", "userData", "_error$response3", "_error$response3$data", "_error$response3$data2", "_error$response4", "_error$response4$data", "_error$response5", "errors", "logout", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/Downloads/lms_backend/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authAPI } from '../services/api';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // Check if user is authenticated on app load\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n\n  const checkAuthStatus = async () => {\n    try {\n      const response = await authAPI.getCurrentUser();\n      if (response.data.is_authenticated) {\n        setUser(response.data.user);\n        setIsAuthenticated(true);\n      }\n    } catch (error) {\n      console.error('Auth check failed:', error);\n      setUser(null);\n      setIsAuthenticated(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const login = async (credentials) => {\n    try {\n      const response = await authAPI.login(credentials);\n      setUser(response.data.user);\n      setIsAuthenticated(true);\n      return { success: true, message: response.data.message };\n    } catch (error) {\n      const errorMessage = error.response?.data?.non_field_errors?.[0] || \n                          error.response?.data?.detail || \n                          'Login failed';\n      return { success: false, message: errorMessage };\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      const response = await authAPI.register(userData);\n      setUser(response.data.user);\n      setIsAuthenticated(true);\n      return { success: true, message: response.data.message };\n    } catch (error) {\n      const errorMessage = error.response?.data?.non_field_errors?.[0] || \n                          error.response?.data?.detail || \n                          'Registration failed';\n      return { success: false, message: errorMessage, errors: error.response?.data };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await authAPI.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setUser(null);\n      setIsAuthenticated(false);\n    }\n  };\n\n  const value = {\n    user,\n    isAuthenticated,\n    loading,\n    login,\n    register,\n    logout,\n    checkAuthStatus,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,eAAe,EAAEC,kBAAkB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACdkB,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMlB,OAAO,CAACmB,cAAc,CAAC,CAAC;MAC/C,IAAID,QAAQ,CAACE,IAAI,CAACC,gBAAgB,EAAE;QAClCT,OAAO,CAACM,QAAQ,CAACE,IAAI,CAACT,IAAI,CAAC;QAC3BK,kBAAkB,CAAC,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CV,OAAO,CAAC,IAAI,CAAC;MACbI,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMlB,OAAO,CAACwB,KAAK,CAACC,WAAW,CAAC;MACjDb,OAAO,CAACM,QAAQ,CAACE,IAAI,CAACT,IAAI,CAAC;MAC3BK,kBAAkB,CAAC,IAAI,CAAC;MACxB,OAAO;QAAEU,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAET,QAAQ,CAACE,IAAI,CAACO;MAAQ,CAAC;IAC1D,CAAC,CAAC,OAAOL,KAAK,EAAE;MAAA,IAAAM,eAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd,MAAMC,YAAY,GAAG,EAAAL,eAAA,GAAAN,KAAK,CAACJ,QAAQ,cAAAU,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBR,IAAI,cAAAS,oBAAA,wBAAAC,qBAAA,GAApBD,oBAAA,CAAsBK,gBAAgB,cAAAJ,qBAAA,uBAAtCA,qBAAA,CAAyC,CAAC,CAAC,OAAAC,gBAAA,GAC5CT,KAAK,CAACJ,QAAQ,cAAAa,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBX,IAAI,cAAAY,qBAAA,uBAApBA,qBAAA,CAAsBG,MAAM,KAC5B,cAAc;MAClC,OAAO;QAAET,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAEM;MAAa,CAAC;IAClD;EACF,CAAC;EAED,MAAMG,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnC,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMlB,OAAO,CAACoC,QAAQ,CAACC,QAAQ,CAAC;MACjDzB,OAAO,CAACM,QAAQ,CAACE,IAAI,CAACT,IAAI,CAAC;MAC3BK,kBAAkB,CAAC,IAAI,CAAC;MACxB,OAAO;QAAEU,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAET,QAAQ,CAACE,IAAI,CAACO;MAAQ,CAAC;IAC1D,CAAC,CAAC,OAAOL,KAAK,EAAE;MAAA,IAAAgB,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA;MACd,MAAMV,YAAY,GAAG,EAAAK,gBAAA,GAAAhB,KAAK,CAACJ,QAAQ,cAAAoB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlB,IAAI,cAAAmB,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAsBL,gBAAgB,cAAAM,sBAAA,uBAAtCA,sBAAA,CAAyC,CAAC,CAAC,OAAAC,gBAAA,GAC5CnB,KAAK,CAACJ,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBP,MAAM,KAC5B,qBAAqB;MACzC,OAAO;QAAET,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAEM,YAAY;QAAEW,MAAM,GAAAD,gBAAA,GAAErB,KAAK,CAACJ,QAAQ,cAAAyB,gBAAA,uBAAdA,gBAAA,CAAgBvB;MAAK,CAAC;IAChF;EACF,CAAC;EAED,MAAMyB,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAM7C,OAAO,CAAC6C,MAAM,CAAC,CAAC;IACxB,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACRV,OAAO,CAAC,IAAI,CAAC;MACbI,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAM8B,KAAK,GAAG;IACZnC,IAAI;IACJI,eAAe;IACfF,OAAO;IACPW,KAAK;IACLY,QAAQ;IACRS,MAAM;IACN5B;EACF,CAAC;EAED,oBACEf,OAAA,CAACC,WAAW,CAAC4C,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAArC,QAAA,EAChCA;EAAQ;IAAAuC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACzC,GAAA,CAhFWF,YAAY;AAAA4C,EAAA,GAAZ5C,YAAY;AAAA,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}