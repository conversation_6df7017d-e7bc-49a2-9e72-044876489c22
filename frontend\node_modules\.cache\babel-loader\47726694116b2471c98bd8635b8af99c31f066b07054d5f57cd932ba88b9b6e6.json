{"ast": null, "code": "var _jsxFileName = \"E:\\\\Downloads\\\\lms_backend\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useLocation } from 'react-router-dom';\nimport Navbar from './Navbar';\nimport Footer from './Footer';\nimport { homeAPI } from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const location = useLocation();\n  const [categories, setCategories] = useState([]);\n  const isHomePage = location.pathname === '/';\n  useEffect(() => {\n    // Load categories for footer\n    const loadCategories = async () => {\n      try {\n        const response = await homeAPI.getHomeData();\n        setCategories(response.data.categories || []);\n      } catch (error) {\n        console.error('Failed to load categories:', error);\n      }\n    };\n    loadCategories();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex flex-column min-vh-100\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `flex-grow-1 ${!isHomePage ? 'main-content' : 'main-content home-page'}`,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {\n      categories: categories\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"C/8blBAXGKfKlrO9qSq02kkBHCA=\", false, function () {\n  return [useLocation];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useLocation", "<PERSON><PERSON><PERSON>", "Footer", "homeAPI", "jsxDEV", "_jsxDEV", "Layout", "children", "_s", "location", "categories", "setCategories", "isHomePage", "pathname", "loadCategories", "response", "getHomeData", "data", "error", "console", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/Downloads/lms_backend/frontend/src/components/layout/Layout.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLocation } from 'react-router-dom';\nimport Navbar from './Navbar';\nimport Footer from './Footer';\nimport { homeAPI } from '../../services/api';\n\nconst Layout = ({ children }) => {\n  const location = useLocation();\n  const [categories, setCategories] = useState([]);\n  const isHomePage = location.pathname === '/';\n\n  useEffect(() => {\n    // Load categories for footer\n    const loadCategories = async () => {\n      try {\n        const response = await homeAPI.getHomeData();\n        setCategories(response.data.categories || []);\n      } catch (error) {\n        console.error('Failed to load categories:', error);\n      }\n    };\n\n    loadCategories();\n  }, []);\n\n  return (\n    <div className=\"d-flex flex-column min-vh-100\">\n      <Navbar />\n      \n      <main className={`flex-grow-1 ${!isHomePage ? 'main-content' : 'main-content home-page'}`}>\n        {children}\n      </main>\n      \n      <Footer categories={categories} />\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,OAAO,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,MAAM,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/B,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAMc,UAAU,GAAGH,QAAQ,CAACI,QAAQ,KAAK,GAAG;EAE5Cd,SAAS,CAAC,MAAM;IACd;IACA,MAAMe,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMZ,OAAO,CAACa,WAAW,CAAC,CAAC;QAC5CL,aAAa,CAACI,QAAQ,CAACE,IAAI,CAACP,UAAU,IAAI,EAAE,CAAC;MAC/C,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD;IACF,CAAC;IAEDJ,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACET,OAAA;IAAKe,SAAS,EAAC,+BAA+B;IAAAb,QAAA,gBAC5CF,OAAA,CAACJ,MAAM;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEVnB,OAAA;MAAMe,SAAS,EAAE,eAAe,CAACR,UAAU,GAAG,cAAc,GAAG,wBAAwB,EAAG;MAAAL,QAAA,EACvFA;IAAQ;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEPnB,OAAA,CAACH,MAAM;MAACQ,UAAU,EAAEA;IAAW;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/B,CAAC;AAEV,CAAC;AAAChB,EAAA,CA9BIF,MAAM;EAAA,QACON,WAAW;AAAA;AAAAyB,EAAA,GADxBnB,MAAM;AAgCZ,eAAeA,MAAM;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}