{"ast": null, "code": "'use strict';\n\nvar utils = require('../utils');\nvar normalizeHeaderName = require('../helpers/normalizeHeaderName');\nvar AxiosError = require('../core/AxiosError');\nvar transitionalDefaults = require('./transitional');\nvar toFormData = require('../helpers/toFormData');\nvar DEFAULT_CONTENT_TYPE = {\n  'Content-Type': 'application/x-www-form-urlencoded'\n};\nfunction setContentTypeIfUnset(headers, value) {\n  if (!utils.isUndefined(headers) && utils.isUndefined(headers['Content-Type'])) {\n    headers['Content-Type'] = value;\n  }\n}\nfunction getDefaultAdapter() {\n  var adapter;\n  if (typeof XMLHttpRequest !== 'undefined') {\n    // For browsers use XHR adapter\n    adapter = require('../adapters/xhr');\n  } else if (typeof process !== 'undefined' && Object.prototype.toString.call(process) === '[object process]') {\n    // For node use HTTP adapter\n    adapter = require('../adapters/http');\n  }\n  return adapter;\n}\nfunction stringifySafely(rawValue, parser, encoder) {\n  if (utils.isString(rawValue)) {\n    try {\n      (parser || JSON.parse)(rawValue);\n      return utils.trim(rawValue);\n    } catch (e) {\n      if (e.name !== 'SyntaxError') {\n        throw e;\n      }\n    }\n  }\n  return (encoder || JSON.stringify)(rawValue);\n}\nvar defaults = {\n  transitional: transitionalDefaults,\n  adapter: getDefaultAdapter(),\n  transformRequest: [function transformRequest(data, headers) {\n    normalizeHeaderName(headers, 'Accept');\n    normalizeHeaderName(headers, 'Content-Type');\n    if (utils.isFormData(data) || utils.isArrayBuffer(data) || utils.isBuffer(data) || utils.isStream(data) || utils.isFile(data) || utils.isBlob(data)) {\n      return data;\n    }\n    if (utils.isArrayBufferView(data)) {\n      return data.buffer;\n    }\n    if (utils.isURLSearchParams(data)) {\n      setContentTypeIfUnset(headers, 'application/x-www-form-urlencoded;charset=utf-8');\n      return data.toString();\n    }\n    var isObjectPayload = utils.isObject(data);\n    var contentType = headers && headers['Content-Type'];\n    var isFileList;\n    if ((isFileList = utils.isFileList(data)) || isObjectPayload && contentType === 'multipart/form-data') {\n      var _FormData = this.env && this.env.FormData;\n      return toFormData(isFileList ? {\n        'files[]': data\n      } : data, _FormData && new _FormData());\n    } else if (isObjectPayload || contentType === 'application/json') {\n      setContentTypeIfUnset(headers, 'application/json');\n      return stringifySafely(data);\n    }\n    return data;\n  }],\n  transformResponse: [function transformResponse(data) {\n    var transitional = this.transitional || defaults.transitional;\n    var silentJSONParsing = transitional && transitional.silentJSONParsing;\n    var forcedJSONParsing = transitional && transitional.forcedJSONParsing;\n    var strictJSONParsing = !silentJSONParsing && this.responseType === 'json';\n    if (strictJSONParsing || forcedJSONParsing && utils.isString(data) && data.length) {\n      try {\n        return JSON.parse(data);\n      } catch (e) {\n        if (strictJSONParsing) {\n          if (e.name === 'SyntaxError') {\n            throw AxiosError.from(e, AxiosError.ERR_BAD_RESPONSE, this, null, this.response);\n          }\n          throw e;\n        }\n      }\n    }\n    return data;\n  }],\n  /**\n   * A timeout in milliseconds to abort a request. If set to 0 (default) a\n   * timeout is not created.\n   */\n  timeout: 0,\n  xsrfCookieName: 'XSRF-TOKEN',\n  xsrfHeaderName: 'X-XSRF-TOKEN',\n  maxContentLength: -1,\n  maxBodyLength: -1,\n  env: {\n    FormData: require('./env/FormData')\n  },\n  validateStatus: function validateStatus(status) {\n    return status >= 200 && status < 300;\n  },\n  headers: {\n    common: {\n      'Accept': 'application/json, text/plain, */*'\n    }\n  }\n};\nutils.forEach(['delete', 'get', 'head'], function forEachMethodNoData(method) {\n  defaults.headers[method] = {};\n});\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  defaults.headers[method] = utils.merge(DEFAULT_CONTENT_TYPE);\n});\nmodule.exports = defaults;", "map": {"version": 3, "names": ["utils", "require", "normalizeHeaderName", "AxiosError", "transitionalD<PERSON>ault<PERSON>", "toFormData", "DEFAULT_CONTENT_TYPE", "setContentTypeIfUnset", "headers", "value", "isUndefined", "getDefaultAdapter", "adapter", "XMLHttpRequest", "process", "Object", "prototype", "toString", "call", "stringifySafely", "rawValue", "parser", "encoder", "isString", "JSON", "parse", "trim", "e", "name", "stringify", "defaults", "transitional", "transformRequest", "data", "isFormData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "isStream", "isFile", "isBlob", "isArrayBuffer<PERSON>iew", "buffer", "isURLSearchParams", "isObjectPayload", "isObject", "contentType", "isFileList", "_FormData", "env", "FormData", "transformResponse", "silentJSONParsing", "forcedJSONParsing", "strictJSONParsing", "responseType", "length", "from", "ERR_BAD_RESPONSE", "response", "timeout", "xsrfCookieName", "xsrfHeaderName", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "validateStatus", "status", "common", "for<PERSON>ach", "forEachMethodNoData", "method", "forEachMethodWithData", "merge", "module", "exports"], "sources": ["E:/Downloads/lms_backend/frontend/node_modules/axios/lib/defaults/index.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('../utils');\nvar normalizeHeaderName = require('../helpers/normalizeHeaderName');\nvar AxiosError = require('../core/AxiosError');\nvar transitionalDefaults = require('./transitional');\nvar toFormData = require('../helpers/toFormData');\n\nvar DEFAULT_CONTENT_TYPE = {\n  'Content-Type': 'application/x-www-form-urlencoded'\n};\n\nfunction setContentTypeIfUnset(headers, value) {\n  if (!utils.isUndefined(headers) && utils.isUndefined(headers['Content-Type'])) {\n    headers['Content-Type'] = value;\n  }\n}\n\nfunction getDefaultAdapter() {\n  var adapter;\n  if (typeof XMLHttpRequest !== 'undefined') {\n    // For browsers use XHR adapter\n    adapter = require('../adapters/xhr');\n  } else if (typeof process !== 'undefined' && Object.prototype.toString.call(process) === '[object process]') {\n    // For node use HTTP adapter\n    adapter = require('../adapters/http');\n  }\n  return adapter;\n}\n\nfunction stringifySafely(rawValue, parser, encoder) {\n  if (utils.isString(rawValue)) {\n    try {\n      (parser || JSON.parse)(rawValue);\n      return utils.trim(rawValue);\n    } catch (e) {\n      if (e.name !== 'SyntaxError') {\n        throw e;\n      }\n    }\n  }\n\n  return (encoder || JSON.stringify)(rawValue);\n}\n\nvar defaults = {\n\n  transitional: transitionalDefaults,\n\n  adapter: getDefaultAdapter(),\n\n  transformRequest: [function transformRequest(data, headers) {\n    normalizeHeaderName(headers, 'Accept');\n    normalizeHeaderName(headers, 'Content-Type');\n\n    if (utils.isFormData(data) ||\n      utils.isArrayBuffer(data) ||\n      utils.isBuffer(data) ||\n      utils.isStream(data) ||\n      utils.isFile(data) ||\n      utils.isBlob(data)\n    ) {\n      return data;\n    }\n    if (utils.isArrayBufferView(data)) {\n      return data.buffer;\n    }\n    if (utils.isURLSearchParams(data)) {\n      setContentTypeIfUnset(headers, 'application/x-www-form-urlencoded;charset=utf-8');\n      return data.toString();\n    }\n\n    var isObjectPayload = utils.isObject(data);\n    var contentType = headers && headers['Content-Type'];\n\n    var isFileList;\n\n    if ((isFileList = utils.isFileList(data)) || (isObjectPayload && contentType === 'multipart/form-data')) {\n      var _FormData = this.env && this.env.FormData;\n      return toFormData(isFileList ? {'files[]': data} : data, _FormData && new _FormData());\n    } else if (isObjectPayload || contentType === 'application/json') {\n      setContentTypeIfUnset(headers, 'application/json');\n      return stringifySafely(data);\n    }\n\n    return data;\n  }],\n\n  transformResponse: [function transformResponse(data) {\n    var transitional = this.transitional || defaults.transitional;\n    var silentJSONParsing = transitional && transitional.silentJSONParsing;\n    var forcedJSONParsing = transitional && transitional.forcedJSONParsing;\n    var strictJSONParsing = !silentJSONParsing && this.responseType === 'json';\n\n    if (strictJSONParsing || (forcedJSONParsing && utils.isString(data) && data.length)) {\n      try {\n        return JSON.parse(data);\n      } catch (e) {\n        if (strictJSONParsing) {\n          if (e.name === 'SyntaxError') {\n            throw AxiosError.from(e, AxiosError.ERR_BAD_RESPONSE, this, null, this.response);\n          }\n          throw e;\n        }\n      }\n    }\n\n    return data;\n  }],\n\n  /**\n   * A timeout in milliseconds to abort a request. If set to 0 (default) a\n   * timeout is not created.\n   */\n  timeout: 0,\n\n  xsrfCookieName: 'XSRF-TOKEN',\n  xsrfHeaderName: 'X-XSRF-TOKEN',\n\n  maxContentLength: -1,\n  maxBodyLength: -1,\n\n  env: {\n    FormData: require('./env/FormData')\n  },\n\n  validateStatus: function validateStatus(status) {\n    return status >= 200 && status < 300;\n  },\n\n  headers: {\n    common: {\n      'Accept': 'application/json, text/plain, */*'\n    }\n  }\n};\n\nutils.forEach(['delete', 'get', 'head'], function forEachMethodNoData(method) {\n  defaults.headers[method] = {};\n});\n\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  defaults.headers[method] = utils.merge(DEFAULT_CONTENT_TYPE);\n});\n\nmodule.exports = defaults;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,UAAU,CAAC;AAC/B,IAAIC,mBAAmB,GAAGD,OAAO,CAAC,gCAAgC,CAAC;AACnE,IAAIE,UAAU,GAAGF,OAAO,CAAC,oBAAoB,CAAC;AAC9C,IAAIG,oBAAoB,GAAGH,OAAO,CAAC,gBAAgB,CAAC;AACpD,IAAII,UAAU,GAAGJ,OAAO,CAAC,uBAAuB,CAAC;AAEjD,IAAIK,oBAAoB,GAAG;EACzB,cAAc,EAAE;AAClB,CAAC;AAED,SAASC,qBAAqBA,CAACC,OAAO,EAAEC,KAAK,EAAE;EAC7C,IAAI,CAACT,KAAK,CAACU,WAAW,CAACF,OAAO,CAAC,IAAIR,KAAK,CAACU,WAAW,CAACF,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE;IAC7EA,OAAO,CAAC,cAAc,CAAC,GAAGC,KAAK;EACjC;AACF;AAEA,SAASE,iBAAiBA,CAAA,EAAG;EAC3B,IAAIC,OAAO;EACX,IAAI,OAAOC,cAAc,KAAK,WAAW,EAAE;IACzC;IACAD,OAAO,GAAGX,OAAO,CAAC,iBAAiB,CAAC;EACtC,CAAC,MAAM,IAAI,OAAOa,OAAO,KAAK,WAAW,IAAIC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,OAAO,CAAC,KAAK,kBAAkB,EAAE;IAC3G;IACAF,OAAO,GAAGX,OAAO,CAAC,kBAAkB,CAAC;EACvC;EACA,OAAOW,OAAO;AAChB;AAEA,SAASO,eAAeA,CAACC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAClD,IAAItB,KAAK,CAACuB,QAAQ,CAACH,QAAQ,CAAC,EAAE;IAC5B,IAAI;MACF,CAACC,MAAM,IAAIG,IAAI,CAACC,KAAK,EAAEL,QAAQ,CAAC;MAChC,OAAOpB,KAAK,CAAC0B,IAAI,CAACN,QAAQ,CAAC;IAC7B,CAAC,CAAC,OAAOO,CAAC,EAAE;MACV,IAAIA,CAAC,CAACC,IAAI,KAAK,aAAa,EAAE;QAC5B,MAAMD,CAAC;MACT;IACF;EACF;EAEA,OAAO,CAACL,OAAO,IAAIE,IAAI,CAACK,SAAS,EAAET,QAAQ,CAAC;AAC9C;AAEA,IAAIU,QAAQ,GAAG;EAEbC,YAAY,EAAE3B,oBAAoB;EAElCQ,OAAO,EAAED,iBAAiB,CAAC,CAAC;EAE5BqB,gBAAgB,EAAE,CAAC,SAASA,gBAAgBA,CAACC,IAAI,EAAEzB,OAAO,EAAE;IAC1DN,mBAAmB,CAACM,OAAO,EAAE,QAAQ,CAAC;IACtCN,mBAAmB,CAACM,OAAO,EAAE,cAAc,CAAC;IAE5C,IAAIR,KAAK,CAACkC,UAAU,CAACD,IAAI,CAAC,IACxBjC,KAAK,CAACmC,aAAa,CAACF,IAAI,CAAC,IACzBjC,KAAK,CAACoC,QAAQ,CAACH,IAAI,CAAC,IACpBjC,KAAK,CAACqC,QAAQ,CAACJ,IAAI,CAAC,IACpBjC,KAAK,CAACsC,MAAM,CAACL,IAAI,CAAC,IAClBjC,KAAK,CAACuC,MAAM,CAACN,IAAI,CAAC,EAClB;MACA,OAAOA,IAAI;IACb;IACA,IAAIjC,KAAK,CAACwC,iBAAiB,CAACP,IAAI,CAAC,EAAE;MACjC,OAAOA,IAAI,CAACQ,MAAM;IACpB;IACA,IAAIzC,KAAK,CAAC0C,iBAAiB,CAACT,IAAI,CAAC,EAAE;MACjC1B,qBAAqB,CAACC,OAAO,EAAE,iDAAiD,CAAC;MACjF,OAAOyB,IAAI,CAAChB,QAAQ,CAAC,CAAC;IACxB;IAEA,IAAI0B,eAAe,GAAG3C,KAAK,CAAC4C,QAAQ,CAACX,IAAI,CAAC;IAC1C,IAAIY,WAAW,GAAGrC,OAAO,IAAIA,OAAO,CAAC,cAAc,CAAC;IAEpD,IAAIsC,UAAU;IAEd,IAAI,CAACA,UAAU,GAAG9C,KAAK,CAAC8C,UAAU,CAACb,IAAI,CAAC,KAAMU,eAAe,IAAIE,WAAW,KAAK,qBAAsB,EAAE;MACvG,IAAIE,SAAS,GAAG,IAAI,CAACC,GAAG,IAAI,IAAI,CAACA,GAAG,CAACC,QAAQ;MAC7C,OAAO5C,UAAU,CAACyC,UAAU,GAAG;QAAC,SAAS,EAAEb;MAAI,CAAC,GAAGA,IAAI,EAAEc,SAAS,IAAI,IAAIA,SAAS,CAAC,CAAC,CAAC;IACxF,CAAC,MAAM,IAAIJ,eAAe,IAAIE,WAAW,KAAK,kBAAkB,EAAE;MAChEtC,qBAAqB,CAACC,OAAO,EAAE,kBAAkB,CAAC;MAClD,OAAOW,eAAe,CAACc,IAAI,CAAC;IAC9B;IAEA,OAAOA,IAAI;EACb,CAAC,CAAC;EAEFiB,iBAAiB,EAAE,CAAC,SAASA,iBAAiBA,CAACjB,IAAI,EAAE;IACnD,IAAIF,YAAY,GAAG,IAAI,CAACA,YAAY,IAAID,QAAQ,CAACC,YAAY;IAC7D,IAAIoB,iBAAiB,GAAGpB,YAAY,IAAIA,YAAY,CAACoB,iBAAiB;IACtE,IAAIC,iBAAiB,GAAGrB,YAAY,IAAIA,YAAY,CAACqB,iBAAiB;IACtE,IAAIC,iBAAiB,GAAG,CAACF,iBAAiB,IAAI,IAAI,CAACG,YAAY,KAAK,MAAM;IAE1E,IAAID,iBAAiB,IAAKD,iBAAiB,IAAIpD,KAAK,CAACuB,QAAQ,CAACU,IAAI,CAAC,IAAIA,IAAI,CAACsB,MAAO,EAAE;MACnF,IAAI;QACF,OAAO/B,IAAI,CAACC,KAAK,CAACQ,IAAI,CAAC;MACzB,CAAC,CAAC,OAAON,CAAC,EAAE;QACV,IAAI0B,iBAAiB,EAAE;UACrB,IAAI1B,CAAC,CAACC,IAAI,KAAK,aAAa,EAAE;YAC5B,MAAMzB,UAAU,CAACqD,IAAI,CAAC7B,CAAC,EAAExB,UAAU,CAACsD,gBAAgB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAACC,QAAQ,CAAC;UAClF;UACA,MAAM/B,CAAC;QACT;MACF;IACF;IAEA,OAAOM,IAAI;EACb,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACE0B,OAAO,EAAE,CAAC;EAEVC,cAAc,EAAE,YAAY;EAC5BC,cAAc,EAAE,cAAc;EAE9BC,gBAAgB,EAAE,CAAC,CAAC;EACpBC,aAAa,EAAE,CAAC,CAAC;EAEjBf,GAAG,EAAE;IACHC,QAAQ,EAAEhD,OAAO,CAAC,gBAAgB;EACpC,CAAC;EAED+D,cAAc,EAAE,SAASA,cAAcA,CAACC,MAAM,EAAE;IAC9C,OAAOA,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG;EACtC,CAAC;EAEDzD,OAAO,EAAE;IACP0D,MAAM,EAAE;MACN,QAAQ,EAAE;IACZ;EACF;AACF,CAAC;AAEDlE,KAAK,CAACmE,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,SAASC,mBAAmBA,CAACC,MAAM,EAAE;EAC5EvC,QAAQ,CAACtB,OAAO,CAAC6D,MAAM,CAAC,GAAG,CAAC,CAAC;AAC/B,CAAC,CAAC;AAEFrE,KAAK,CAACmE,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,SAASG,qBAAqBA,CAACD,MAAM,EAAE;EAC7EvC,QAAQ,CAACtB,OAAO,CAAC6D,MAAM,CAAC,GAAGrE,KAAK,CAACuE,KAAK,CAACjE,oBAAoB,CAAC;AAC9D,CAAC,CAAC;AAEFkE,MAAM,CAACC,OAAO,GAAG3C,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}