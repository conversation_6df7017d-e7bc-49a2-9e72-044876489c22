{"ast": null, "code": "var _jsxFileName = \"E:\\\\Downloads\\\\lms_backend\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport Layout from './components/layout/Layout';\nimport Home from './pages/Home';\nimport Login from './components/auth/Login';\nimport Signup from './components/auth/Signup';\nimport CourseList from './components/courses/CourseList';\nimport CourseDetail from './components/courses/CourseDetail';\nimport Dashboard from './components/dashboard/Dashboard';\nimport ProtectedRoute from './components/common/ProtectedRoute';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Layout, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/signup\",\n            element: /*#__PURE__*/_jsxDEV(Signup, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/courses\",\n            element: /*#__PURE__*/_jsxDEV(CourseList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/dashboard\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "Layout", "Home", "<PERSON><PERSON>", "Signup", "CourseList", "CourseDetail", "Dashboard", "ProtectedRoute", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "$RefreshReg$"], "sources": ["E:/Downloads/lms_backend/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport Layout from './components/layout/Layout';\nimport Home from './pages/Home';\nimport Login from './components/auth/Login';\nimport Signup from './components/auth/Signup';\nimport CourseList from './components/courses/CourseList';\nimport CourseDetail from './components/courses/CourseDetail';\nimport Dashboard from './components/dashboard/Dashboard';\nimport ProtectedRoute from './components/common/ProtectedRoute';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <Layout>\n          <Routes>\n            {/* Public Routes */}\n            <Route path=\"/\" element={<Home />} />\n            <Route path=\"/login\" element={<Login />} />\n            <Route path=\"/signup\" element={<Signup />} />\n\n            {/* Course Routes */}\n            <Route path=\"/courses\" element={<CourseList />} />\n            {/* <Route path=\"/courses/:courseId\" element={<CourseDetail />} />\n            <Route path=\"/courses/:courseId/lessons/:lessonId\" element={<LessonDetail />} /> */}\n\n            {/* Protected Routes */}\n            <Route path=\"/dashboard\" element={\n              <ProtectedRoute>\n                <Dashboard />\n              </ProtectedRoute>\n            } />\n\n            {/* Teacher Routes */}\n            {/* <Route path=\"/teacher/*\" element={\n              <ProtectedRoute requiredRole=\"teacher\">\n                <TeacherRoutes />\n              </ProtectedRoute>\n            } /> */}\n\n            {/* Fallback Route */}\n            <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n          </Routes>\n        </Layout>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,MAAM,MAAM,0BAA0B;AAC7C,OAAOC,UAAU,MAAM,iCAAiC;AACxD,OAAOC,YAAY,MAAM,mCAAmC;AAC5D,OAAOC,SAAS,MAAM,kCAAkC;AACxD,OAAOC,cAAc,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACV,YAAY;IAAAY,QAAA,eACXF,OAAA,CAACd,MAAM;MAAAgB,QAAA,eACLF,OAAA,CAACT,MAAM;QAAAW,QAAA,eACLF,OAAA,CAACb,MAAM;UAAAe,QAAA,gBAELF,OAAA,CAACZ,KAAK;YAACe,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEJ,OAAA,CAACR,IAAI;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrCR,OAAA,CAACZ,KAAK;YAACe,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEJ,OAAA,CAACP,KAAK;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CR,OAAA,CAACZ,KAAK;YAACe,IAAI,EAAC,SAAS;YAACC,OAAO,eAAEJ,OAAA,CAACN,MAAM;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAG7CR,OAAA,CAACZ,KAAK;YAACe,IAAI,EAAC,UAAU;YAACC,OAAO,eAAEJ,OAAA,CAACL,UAAU;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAKlDR,OAAA,CAACZ,KAAK;YAACe,IAAI,EAAC,YAAY;YAACC,OAAO,eAC9BJ,OAAA,CAACF,cAAc;cAAAI,QAAA,eACbF,OAAA,CAACH,SAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAUJR,OAAA,CAACZ,KAAK;YAACe,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEJ,OAAA,CAACX,QAAQ;cAACoB,EAAE,EAAC,GAAG;cAACC,OAAO;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACG,EAAA,GArCQV,GAAG;AAuCZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}