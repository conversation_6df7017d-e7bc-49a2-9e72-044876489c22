{"ast": null, "code": "var _jsxFileName = \"E:\\\\Downloads\\\\lms_backend\\\\frontend\\\\src\\\\components\\\\auth\\\\Signup.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate, useSearchParams } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Signup = () => {\n  _s();\n  const {\n    register,\n    isAuthenticated\n  } = useAuth();\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const [formData, setFormData] = useState({\n    first_name: '',\n    last_name: '',\n    email: '',\n    username: '',\n    password: '',\n    password_confirm: '',\n    role: searchParams.get('role') || 'student',\n    mobile_no: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/dashboard');\n    }\n  }, [isAuthenticated, navigate]);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setErrors({});\n\n    // Client-side validation\n    if (formData.password !== formData.password_confirm) {\n      setErrors({\n        password_confirm: 'Passwords do not match!'\n      });\n      setLoading(false);\n      return;\n    }\n    try {\n      const result = await register(formData);\n      if (result.success) {\n        navigate('/dashboard');\n      } else {\n        setErrors(result.errors || {\n          general: result.message\n        });\n      }\n    } catch (error) {\n      setErrors({\n        general: 'An unexpected error occurred. Please try again.'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container my-5\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row justify-content-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-lg-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card auth-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleSubmit,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-center mb-4\",\n                  children: \"Create Your Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 19\n                }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"alert alert-danger\",\n                  role: \"alert\",\n                  children: errors.general\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-6\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"input-group mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"input-group-text\",\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-user\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 90,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 89,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      className: `form-control ${errors.first_name ? 'is-invalid' : ''}`,\n                      name: \"first_name\",\n                      placeholder: \"First Name\",\n                      value: formData.first_name,\n                      onChange: handleChange,\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 92,\n                      columnNumber: 23\n                    }, this), errors.first_name && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"invalid-feedback\",\n                      children: errors.first_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 102,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 88,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-6\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"input-group mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"input-group-text\",\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-user\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 111,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 110,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      className: `form-control ${errors.last_name ? 'is-invalid' : ''}`,\n                      name: \"last_name\",\n                      placeholder: \"Last Name\",\n                      value: formData.last_name,\n                      onChange: handleChange,\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 113,\n                      columnNumber: 23\n                    }, this), errors.last_name && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"invalid-feedback\",\n                      children: errors.last_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 123,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 109,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"input-group-text\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-envelope\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  className: `form-control ${errors.email ? 'is-invalid' : ''}`,\n                  name: \"email\",\n                  placeholder: \"Email Address\",\n                  value: formData.email,\n                  onChange: handleChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this), errors.email && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"invalid-feedback\",\n                  children: errors.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"input-group-text\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-at\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  className: `form-control ${errors.username ? 'is-invalid' : ''}`,\n                  name: \"username\",\n                  placeholder: \"Username\",\n                  value: formData.username,\n                  onChange: handleChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this), errors.username && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"invalid-feedback\",\n                  children: errors.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"input-group-text\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  className: `form-control ${errors.mobile_no ? 'is-invalid' : ''}`,\n                  name: \"mobile_no\",\n                  placeholder: \"Mobile Number (Optional)\",\n                  value: formData.mobile_no,\n                  onChange: handleChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 19\n                }, this), errors.mobile_no && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"invalid-feedback\",\n                  children: errors.mobile_no\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"input-group-text\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-lock\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"password\",\n                  className: `form-control ${errors.password ? 'is-invalid' : ''}`,\n                  name: \"password\",\n                  placeholder: \"Password\",\n                  value: formData.password,\n                  onChange: handleChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 19\n                }, this), errors.password && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"invalid-feedback\",\n                  children: errors.password\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"input-group-text\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-lock\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"password\",\n                  className: `form-control ${errors.password_confirm ? 'is-invalid' : ''}`,\n                  name: \"password_confirm\",\n                  placeholder: \"Confirm Password\",\n                  value: formData.password_confirm,\n                  onChange: handleChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this), errors.password_confirm && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"invalid-feedback\",\n                  children: errors.password_confirm\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"input-group-text\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-user-tag\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  className: `form-select ${errors.role ? 'is-invalid' : ''}`,\n                  name: \"role\",\n                  value: formData.role,\n                  onChange: handleChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"student\",\n                    children: \"Student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"teacher\",\n                    children: \"Teacher\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this), errors.role && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"invalid-feedback\",\n                  children: errors.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-grid\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  className: \"btn btn-primary btn-lg\",\n                  disabled: loading,\n                  children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"spinner-border spinner-border-sm me-2\",\n                      role: \"status\",\n                      \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 259,\n                      columnNumber: 25\n                    }, this), \"Creating Account...\"]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-user-plus me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 264,\n                      columnNumber: 25\n                    }, this), \" Create Account\"]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-footer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-muted\",\n                children: \"Already have an account?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"text-decoration-none ms-1\",\n                children: \"Login Here\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_s(Signup, \"iM7dKNlUFjQbvxPY9oy1IHgFcuQ=\", false, function () {\n  return [useAuth, useNavigate, useSearchParams];\n});\n_c = Signup;\nexport default Signup;\nvar _c;\n$RefreshReg$(_c, \"Signup\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "useSearchParams", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Signup", "_s", "register", "isAuthenticated", "navigate", "searchParams", "formData", "setFormData", "first_name", "last_name", "email", "username", "password", "password_confirm", "role", "get", "mobile_no", "errors", "setErrors", "loading", "setLoading", "handleChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "result", "success", "general", "message", "error", "className", "children", "onSubmit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "onChange", "required", "disabled", "to", "_c", "$RefreshReg$"], "sources": ["E:/Downloads/lms_backend/frontend/src/components/auth/Signup.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate, useSearchParams } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst Signup = () => {\n  const { register, isAuthenticated } = useAuth();\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const [formData, setFormData] = useState({\n    first_name: '',\n    last_name: '',\n    email: '',\n    username: '',\n    password: '',\n    password_confirm: '',\n    role: searchParams.get('role') || 'student',\n    mobile_no: '',\n  });\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/dashboard');\n    }\n  }, [isAuthenticated, navigate]);\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setErrors({});\n\n    // Client-side validation\n    if (formData.password !== formData.password_confirm) {\n      setErrors({ password_confirm: 'Passwords do not match!' });\n      setLoading(false);\n      return;\n    }\n\n    try {\n      const result = await register(formData);\n      if (result.success) {\n        navigate('/dashboard');\n      } else {\n        setErrors(result.errors || { general: result.message });\n      }\n    } catch (error) {\n      setErrors({ general: 'An unexpected error occurred. Please try again.' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"container my-5\">\n      <div className=\"row justify-content-center\">\n        <div className=\"col-lg-6\">\n          <div className=\"card auth-card\">\n            <div className=\"card-body\">\n              <form onSubmit={handleSubmit}>\n                <div className=\"mb-4\">\n                  <h4 className=\"text-center mb-4\">Create Your Account</h4>\n                  {errors.general && (\n                    <div className=\"alert alert-danger\" role=\"alert\">\n                      {errors.general}\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"row\">\n                  <div className=\"col-md-6\">\n                    <div className=\"input-group mb-3\">\n                      <span className=\"input-group-text\">\n                        <i className=\"fas fa-user\"></i>\n                      </span>\n                      <input \n                        type=\"text\" \n                        className={`form-control ${errors.first_name ? 'is-invalid' : ''}`}\n                        name=\"first_name\" \n                        placeholder=\"First Name\" \n                        value={formData.first_name}\n                        onChange={handleChange}\n                        required \n                      />\n                      {errors.first_name && (\n                        <div className=\"invalid-feedback\">\n                          {errors.first_name}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                  <div className=\"col-md-6\">\n                    <div className=\"input-group mb-3\">\n                      <span className=\"input-group-text\">\n                        <i className=\"fas fa-user\"></i>\n                      </span>\n                      <input \n                        type=\"text\" \n                        className={`form-control ${errors.last_name ? 'is-invalid' : ''}`}\n                        name=\"last_name\" \n                        placeholder=\"Last Name\" \n                        value={formData.last_name}\n                        onChange={handleChange}\n                        required \n                      />\n                      {errors.last_name && (\n                        <div className=\"invalid-feedback\">\n                          {errors.last_name}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"input-group mb-3\">\n                  <span className=\"input-group-text\">\n                    <i className=\"fas fa-envelope\"></i>\n                  </span>\n                  <input \n                    type=\"email\" \n                    className={`form-control ${errors.email ? 'is-invalid' : ''}`}\n                    name=\"email\" \n                    placeholder=\"Email Address\" \n                    value={formData.email}\n                    onChange={handleChange}\n                    required \n                  />\n                  {errors.email && (\n                    <div className=\"invalid-feedback\">\n                      {errors.email}\n                    </div>\n                  )}\n                </div>\n                \n                <div className=\"input-group mb-3\">\n                  <span className=\"input-group-text\">\n                    <i className=\"fas fa-at\"></i>\n                  </span>\n                  <input \n                    type=\"text\" \n                    className={`form-control ${errors.username ? 'is-invalid' : ''}`}\n                    name=\"username\" \n                    placeholder=\"Username\" \n                    value={formData.username}\n                    onChange={handleChange}\n                    required \n                  />\n                  {errors.username && (\n                    <div className=\"invalid-feedback\">\n                      {errors.username}\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"input-group mb-3\">\n                  <span className=\"input-group-text\">\n                    <i className=\"fas fa-phone\"></i>\n                  </span>\n                  <input \n                    type=\"tel\" \n                    className={`form-control ${errors.mobile_no ? 'is-invalid' : ''}`}\n                    name=\"mobile_no\" \n                    placeholder=\"Mobile Number (Optional)\" \n                    value={formData.mobile_no}\n                    onChange={handleChange}\n                  />\n                  {errors.mobile_no && (\n                    <div className=\"invalid-feedback\">\n                      {errors.mobile_no}\n                    </div>\n                  )}\n                </div>\n                \n                <div className=\"input-group mb-3\">\n                  <span className=\"input-group-text\">\n                    <i className=\"fas fa-lock\"></i>\n                  </span>\n                  <input \n                    type=\"password\" \n                    className={`form-control ${errors.password ? 'is-invalid' : ''}`}\n                    name=\"password\" \n                    placeholder=\"Password\" \n                    value={formData.password}\n                    onChange={handleChange}\n                    required \n                  />\n                  {errors.password && (\n                    <div className=\"invalid-feedback\">\n                      {errors.password}\n                    </div>\n                  )}\n                </div>\n                \n                <div className=\"input-group mb-3\">\n                  <span className=\"input-group-text\">\n                    <i className=\"fas fa-lock\"></i>\n                  </span>\n                  <input \n                    type=\"password\" \n                    className={`form-control ${errors.password_confirm ? 'is-invalid' : ''}`}\n                    name=\"password_confirm\" \n                    placeholder=\"Confirm Password\" \n                    value={formData.password_confirm}\n                    onChange={handleChange}\n                    required \n                  />\n                  {errors.password_confirm && (\n                    <div className=\"invalid-feedback\">\n                      {errors.password_confirm}\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"input-group mb-3\">\n                  <span className=\"input-group-text\">\n                    <i className=\"fas fa-user-tag\"></i>\n                  </span>\n                  <select \n                    className={`form-select ${errors.role ? 'is-invalid' : ''}`}\n                    name=\"role\" \n                    value={formData.role}\n                    onChange={handleChange}\n                    required\n                  >\n                    <option value=\"student\">Student</option>\n                    <option value=\"teacher\">Teacher</option>\n                  </select>\n                  {errors.role && (\n                    <div className=\"invalid-feedback\">\n                      {errors.role}\n                    </div>\n                  )}\n                </div>\n                \n                <div className=\"d-grid\">\n                  <button \n                    type=\"submit\" \n                    className=\"btn btn-primary btn-lg\"\n                    disabled={loading}\n                  >\n                    {loading ? (\n                      <>\n                        <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                        Creating Account...\n                      </>\n                    ) : (\n                      <>\n                        <i className=\"fas fa-user-plus me-2\"></i> Create Account\n                      </>\n                    )}\n                  </button>\n                </div>\n              </form>\n            </div>\n            <div className=\"card-footer\">\n              <div className=\"text-center\">\n                <span className=\"text-muted\">Already have an account?</span>\n                <Link to=\"/login\" className=\"text-decoration-none ms-1\">Login Here</Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Signup;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AACrE,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC,QAAQ;IAAEC;EAAgB,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC/C,MAAMS,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,YAAY,CAAC,GAAGX,eAAe,CAAC,CAAC;EACxC,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,gBAAgB,EAAE,EAAE;IACpBC,IAAI,EAAET,YAAY,CAACU,GAAG,CAAC,MAAM,CAAC,IAAI,SAAS;IAC3CC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACAC,SAAS,CAAC,MAAM;IACd,IAAIY,eAAe,EAAE;MACnBC,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACD,eAAe,EAAEC,QAAQ,CAAC,CAAC;EAE/B,MAAMiB,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChClB,WAAW,CAACmB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;IACH;IACA,IAAIP,MAAM,CAACM,IAAI,CAAC,EAAE;MAChBL,SAAS,CAACQ,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACH,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOL,CAAC,IAAK;IAChCA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBR,UAAU,CAAC,IAAI,CAAC;IAChBF,SAAS,CAAC,CAAC,CAAC,CAAC;;IAEb;IACA,IAAIZ,QAAQ,CAACM,QAAQ,KAAKN,QAAQ,CAACO,gBAAgB,EAAE;MACnDK,SAAS,CAAC;QAAEL,gBAAgB,EAAE;MAA0B,CAAC,CAAC;MAC1DO,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI;MACF,MAAMS,MAAM,GAAG,MAAM3B,QAAQ,CAACI,QAAQ,CAAC;MACvC,IAAIuB,MAAM,CAACC,OAAO,EAAE;QAClB1B,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,MAAM;QACLc,SAAS,CAACW,MAAM,CAACZ,MAAM,IAAI;UAAEc,OAAO,EAAEF,MAAM,CAACG;QAAQ,CAAC,CAAC;MACzD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdf,SAAS,CAAC;QAAEa,OAAO,EAAE;MAAkD,CAAC,CAAC;IAC3E,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEvB,OAAA;IAAKqC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BtC,OAAA;MAAKqC,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzCtC,OAAA;QAAKqC,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBtC,OAAA;UAAKqC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BtC,OAAA;YAAKqC,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBtC,OAAA;cAAMuC,QAAQ,EAAET,YAAa;cAAAQ,QAAA,gBAC3BtC,OAAA;gBAAKqC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBtC,OAAA;kBAAIqC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACxDvB,MAAM,CAACc,OAAO,iBACblC,OAAA;kBAAKqC,SAAS,EAAC,oBAAoB;kBAACpB,IAAI,EAAC,OAAO;kBAAAqB,QAAA,EAC7ClB,MAAM,CAACc;gBAAO;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN3C,OAAA;gBAAKqC,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBtC,OAAA;kBAAKqC,SAAS,EAAC,UAAU;kBAAAC,QAAA,eACvBtC,OAAA;oBAAKqC,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BtC,OAAA;sBAAMqC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eAChCtC,OAAA;wBAAGqC,SAAS,EAAC;sBAAa;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC,eACP3C,OAAA;sBACE4C,IAAI,EAAC,MAAM;sBACXP,SAAS,EAAE,gBAAgBjB,MAAM,CAACT,UAAU,GAAG,YAAY,GAAG,EAAE,EAAG;sBACnEe,IAAI,EAAC,YAAY;sBACjBmB,WAAW,EAAC,YAAY;sBACxBlB,KAAK,EAAElB,QAAQ,CAACE,UAAW;sBAC3BmC,QAAQ,EAAEtB,YAAa;sBACvBuB,QAAQ;oBAAA;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC,EACDvB,MAAM,CAACT,UAAU,iBAChBX,OAAA;sBAAKqC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAC9BlB,MAAM,CAACT;oBAAU;sBAAA6B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3C,OAAA;kBAAKqC,SAAS,EAAC,UAAU;kBAAAC,QAAA,eACvBtC,OAAA;oBAAKqC,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BtC,OAAA;sBAAMqC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eAChCtC,OAAA;wBAAGqC,SAAS,EAAC;sBAAa;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC,eACP3C,OAAA;sBACE4C,IAAI,EAAC,MAAM;sBACXP,SAAS,EAAE,gBAAgBjB,MAAM,CAACR,SAAS,GAAG,YAAY,GAAG,EAAE,EAAG;sBAClEc,IAAI,EAAC,WAAW;sBAChBmB,WAAW,EAAC,WAAW;sBACvBlB,KAAK,EAAElB,QAAQ,CAACG,SAAU;sBAC1BkC,QAAQ,EAAEtB,YAAa;sBACvBuB,QAAQ;oBAAA;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC,EACDvB,MAAM,CAACR,SAAS,iBACfZ,OAAA;sBAAKqC,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAC9BlB,MAAM,CAACR;oBAAS;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN3C,OAAA;gBAAKqC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BtC,OAAA;kBAAMqC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAChCtC,OAAA;oBAAGqC,SAAS,EAAC;kBAAiB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACP3C,OAAA;kBACE4C,IAAI,EAAC,OAAO;kBACZP,SAAS,EAAE,gBAAgBjB,MAAM,CAACP,KAAK,GAAG,YAAY,GAAG,EAAE,EAAG;kBAC9Da,IAAI,EAAC,OAAO;kBACZmB,WAAW,EAAC,eAAe;kBAC3BlB,KAAK,EAAElB,QAAQ,CAACI,KAAM;kBACtBiC,QAAQ,EAAEtB,YAAa;kBACvBuB,QAAQ;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,EACDvB,MAAM,CAACP,KAAK,iBACXb,OAAA;kBAAKqC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAC9BlB,MAAM,CAACP;gBAAK;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN3C,OAAA;gBAAKqC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BtC,OAAA;kBAAMqC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAChCtC,OAAA;oBAAGqC,SAAS,EAAC;kBAAW;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACP3C,OAAA;kBACE4C,IAAI,EAAC,MAAM;kBACXP,SAAS,EAAE,gBAAgBjB,MAAM,CAACN,QAAQ,GAAG,YAAY,GAAG,EAAE,EAAG;kBACjEY,IAAI,EAAC,UAAU;kBACfmB,WAAW,EAAC,UAAU;kBACtBlB,KAAK,EAAElB,QAAQ,CAACK,QAAS;kBACzBgC,QAAQ,EAAEtB,YAAa;kBACvBuB,QAAQ;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,EACDvB,MAAM,CAACN,QAAQ,iBACdd,OAAA;kBAAKqC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAC9BlB,MAAM,CAACN;gBAAQ;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN3C,OAAA;gBAAKqC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BtC,OAAA;kBAAMqC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAChCtC,OAAA;oBAAGqC,SAAS,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACP3C,OAAA;kBACE4C,IAAI,EAAC,KAAK;kBACVP,SAAS,EAAE,gBAAgBjB,MAAM,CAACD,SAAS,GAAG,YAAY,GAAG,EAAE,EAAG;kBAClEO,IAAI,EAAC,WAAW;kBAChBmB,WAAW,EAAC,0BAA0B;kBACtClB,KAAK,EAAElB,QAAQ,CAACU,SAAU;kBAC1B2B,QAAQ,EAAEtB;gBAAa;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,EACDvB,MAAM,CAACD,SAAS,iBACfnB,OAAA;kBAAKqC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAC9BlB,MAAM,CAACD;gBAAS;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN3C,OAAA;gBAAKqC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BtC,OAAA;kBAAMqC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAChCtC,OAAA;oBAAGqC,SAAS,EAAC;kBAAa;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACP3C,OAAA;kBACE4C,IAAI,EAAC,UAAU;kBACfP,SAAS,EAAE,gBAAgBjB,MAAM,CAACL,QAAQ,GAAG,YAAY,GAAG,EAAE,EAAG;kBACjEW,IAAI,EAAC,UAAU;kBACfmB,WAAW,EAAC,UAAU;kBACtBlB,KAAK,EAAElB,QAAQ,CAACM,QAAS;kBACzB+B,QAAQ,EAAEtB,YAAa;kBACvBuB,QAAQ;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,EACDvB,MAAM,CAACL,QAAQ,iBACdf,OAAA;kBAAKqC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAC9BlB,MAAM,CAACL;gBAAQ;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN3C,OAAA;gBAAKqC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BtC,OAAA;kBAAMqC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAChCtC,OAAA;oBAAGqC,SAAS,EAAC;kBAAa;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACP3C,OAAA;kBACE4C,IAAI,EAAC,UAAU;kBACfP,SAAS,EAAE,gBAAgBjB,MAAM,CAACJ,gBAAgB,GAAG,YAAY,GAAG,EAAE,EAAG;kBACzEU,IAAI,EAAC,kBAAkB;kBACvBmB,WAAW,EAAC,kBAAkB;kBAC9BlB,KAAK,EAAElB,QAAQ,CAACO,gBAAiB;kBACjC8B,QAAQ,EAAEtB,YAAa;kBACvBuB,QAAQ;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,EACDvB,MAAM,CAACJ,gBAAgB,iBACtBhB,OAAA;kBAAKqC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAC9BlB,MAAM,CAACJ;gBAAgB;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN3C,OAAA;gBAAKqC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BtC,OAAA;kBAAMqC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAChCtC,OAAA;oBAAGqC,SAAS,EAAC;kBAAiB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACP3C,OAAA;kBACEqC,SAAS,EAAE,eAAejB,MAAM,CAACH,IAAI,GAAG,YAAY,GAAG,EAAE,EAAG;kBAC5DS,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAElB,QAAQ,CAACQ,IAAK;kBACrB6B,QAAQ,EAAEtB,YAAa;kBACvBuB,QAAQ;kBAAAT,QAAA,gBAERtC,OAAA;oBAAQ2B,KAAK,EAAC,SAAS;oBAAAW,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxC3C,OAAA;oBAAQ2B,KAAK,EAAC,SAAS;oBAAAW,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,EACRvB,MAAM,CAACH,IAAI,iBACVjB,OAAA;kBAAKqC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAC9BlB,MAAM,CAACH;gBAAI;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN3C,OAAA;gBAAKqC,SAAS,EAAC,QAAQ;gBAAAC,QAAA,eACrBtC,OAAA;kBACE4C,IAAI,EAAC,QAAQ;kBACbP,SAAS,EAAC,wBAAwB;kBAClCW,QAAQ,EAAE1B,OAAQ;kBAAAgB,QAAA,EAEjBhB,OAAO,gBACNtB,OAAA,CAAAE,SAAA;oBAAAoC,QAAA,gBACEtC,OAAA;sBAAMqC,SAAS,EAAC,uCAAuC;sBAACpB,IAAI,EAAC,QAAQ;sBAAC,eAAY;oBAAM;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,uBAElG;kBAAA,eAAE,CAAC,gBAEH3C,OAAA,CAAAE,SAAA;oBAAAoC,QAAA,gBACEtC,OAAA;sBAAGqC,SAAS,EAAC;oBAAuB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,mBAC3C;kBAAA,eAAE;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN3C,OAAA;YAAKqC,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BtC,OAAA;cAAKqC,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BtC,OAAA;gBAAMqC,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5D3C,OAAA,CAACL,IAAI;gBAACsD,EAAE,EAAC,QAAQ;gBAACZ,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvC,EAAA,CArRID,MAAM;EAAA,QAC4BL,OAAO,EAC5BF,WAAW,EACLC,eAAe;AAAA;AAAAqD,EAAA,GAHlC/C,MAAM;AAuRZ,eAAeA,MAAM;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}