{"ast": null, "code": "'use strict';\n\nvar utils = require('./../utils');\nvar buildURL = require('../helpers/buildURL');\nvar InterceptorManager = require('./InterceptorManager');\nvar dispatchRequest = require('./dispatchRequest');\nvar mergeConfig = require('./mergeConfig');\nvar buildFullPath = require('./buildFullPath');\nvar validator = require('../helpers/validator');\nvar validators = validator.validators;\n/**\n * Create a new instance of Axios\n *\n * @param {Object} instanceConfig The default config for the instance\n */\nfunction Axios(instanceConfig) {\n  this.defaults = instanceConfig;\n  this.interceptors = {\n    request: new InterceptorManager(),\n    response: new InterceptorManager()\n  };\n}\n\n/**\n * Dispatch a request\n *\n * @param {Object} config The config specific for this request (merged with this.defaults)\n */\nAxios.prototype.request = function request(configOrUrl, config) {\n  /*eslint no-param-reassign:0*/\n  // Allow for axios('example/url'[, config]) a la fetch API\n  if (typeof configOrUrl === 'string') {\n    config = config || {};\n    config.url = configOrUrl;\n  } else {\n    config = configOrUrl || {};\n  }\n  config = mergeConfig(this.defaults, config);\n\n  // Set config.method\n  if (config.method) {\n    config.method = config.method.toLowerCase();\n  } else if (this.defaults.method) {\n    config.method = this.defaults.method.toLowerCase();\n  } else {\n    config.method = 'get';\n  }\n  var transitional = config.transitional;\n  if (transitional !== undefined) {\n    validator.assertOptions(transitional, {\n      silentJSONParsing: validators.transitional(validators.boolean),\n      forcedJSONParsing: validators.transitional(validators.boolean),\n      clarifyTimeoutError: validators.transitional(validators.boolean)\n    }, false);\n  }\n\n  // filter out skipped interceptors\n  var requestInterceptorChain = [];\n  var synchronousRequestInterceptors = true;\n  this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {\n    if (typeof interceptor.runWhen === 'function' && interceptor.runWhen(config) === false) {\n      return;\n    }\n    synchronousRequestInterceptors = synchronousRequestInterceptors && interceptor.synchronous;\n    requestInterceptorChain.unshift(interceptor.fulfilled, interceptor.rejected);\n  });\n  var responseInterceptorChain = [];\n  this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {\n    responseInterceptorChain.push(interceptor.fulfilled, interceptor.rejected);\n  });\n  var promise;\n  if (!synchronousRequestInterceptors) {\n    var chain = [dispatchRequest, undefined];\n    Array.prototype.unshift.apply(chain, requestInterceptorChain);\n    chain = chain.concat(responseInterceptorChain);\n    promise = Promise.resolve(config);\n    while (chain.length) {\n      promise = promise.then(chain.shift(), chain.shift());\n    }\n    return promise;\n  }\n  var newConfig = config;\n  while (requestInterceptorChain.length) {\n    var onFulfilled = requestInterceptorChain.shift();\n    var onRejected = requestInterceptorChain.shift();\n    try {\n      newConfig = onFulfilled(newConfig);\n    } catch (error) {\n      onRejected(error);\n      break;\n    }\n  }\n  try {\n    promise = dispatchRequest(newConfig);\n  } catch (error) {\n    return Promise.reject(error);\n  }\n  while (responseInterceptorChain.length) {\n    promise = promise.then(responseInterceptorChain.shift(), responseInterceptorChain.shift());\n  }\n  return promise;\n};\nAxios.prototype.getUri = function getUri(config) {\n  config = mergeConfig(this.defaults, config);\n  var fullPath = buildFullPath(config.baseURL, config.url);\n  return buildURL(fullPath, config.params, config.paramsSerializer);\n};\n\n// Provide aliases for supported request methods\nutils.forEach(['delete', 'get', 'head', 'options'], function forEachMethodNoData(method) {\n  /*eslint func-names:0*/\n  Axios.prototype[method] = function (url, config) {\n    return this.request(mergeConfig(config || {}, {\n      method: method,\n      url: url,\n      data: (config || {}).data\n    }));\n  };\n});\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  /*eslint func-names:0*/\n\n  function generateHTTPMethod(isForm) {\n    return function httpMethod(url, data, config) {\n      return this.request(mergeConfig(config || {}, {\n        method: method,\n        headers: isForm ? {\n          'Content-Type': 'multipart/form-data'\n        } : {},\n        url: url,\n        data: data\n      }));\n    };\n  }\n  Axios.prototype[method] = generateHTTPMethod();\n  Axios.prototype[method + 'Form'] = generateHTTPMethod(true);\n});\nmodule.exports = Axios;", "map": {"version": 3, "names": ["utils", "require", "buildURL", "InterceptorManager", "dispatchRequest", "mergeConfig", "buildFullPath", "validator", "validators", "A<PERSON>os", "instanceConfig", "defaults", "interceptors", "request", "response", "prototype", "configOrUrl", "config", "url", "method", "toLowerCase", "transitional", "undefined", "assertOptions", "silentJSONParsing", "boolean", "forcedJSONParsing", "clarifyTimeoutError", "requestInterceptorChain", "synchronousRequestInterceptors", "for<PERSON>ach", "unshiftRequestInterceptors", "interceptor", "runWhen", "synchronous", "unshift", "fulfilled", "rejected", "responseInterceptorChain", "pushResponseInterceptors", "push", "promise", "chain", "Array", "apply", "concat", "Promise", "resolve", "length", "then", "shift", "newConfig", "onFulfilled", "onRejected", "error", "reject", "get<PERSON><PERSON>", "fullPath", "baseURL", "params", "paramsSerializer", "forEachMethodNoData", "data", "forEachMethodWithData", "generateHTTPMethod", "isForm", "httpMethod", "headers", "module", "exports"], "sources": ["E:/Downloads/lms_backend/frontend/node_modules/axios/lib/core/Axios.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./../utils');\nvar buildURL = require('../helpers/buildURL');\nvar InterceptorManager = require('./InterceptorManager');\nvar dispatchRequest = require('./dispatchRequest');\nvar mergeConfig = require('./mergeConfig');\nvar buildFullPath = require('./buildFullPath');\nvar validator = require('../helpers/validator');\n\nvar validators = validator.validators;\n/**\n * Create a new instance of Axios\n *\n * @param {Object} instanceConfig The default config for the instance\n */\nfunction Axios(instanceConfig) {\n  this.defaults = instanceConfig;\n  this.interceptors = {\n    request: new InterceptorManager(),\n    response: new InterceptorManager()\n  };\n}\n\n/**\n * Dispatch a request\n *\n * @param {Object} config The config specific for this request (merged with this.defaults)\n */\nAxios.prototype.request = function request(configOrUrl, config) {\n  /*eslint no-param-reassign:0*/\n  // Allow for axios('example/url'[, config]) a la fetch API\n  if (typeof configOrUrl === 'string') {\n    config = config || {};\n    config.url = configOrUrl;\n  } else {\n    config = configOrUrl || {};\n  }\n\n  config = mergeConfig(this.defaults, config);\n\n  // Set config.method\n  if (config.method) {\n    config.method = config.method.toLowerCase();\n  } else if (this.defaults.method) {\n    config.method = this.defaults.method.toLowerCase();\n  } else {\n    config.method = 'get';\n  }\n\n  var transitional = config.transitional;\n\n  if (transitional !== undefined) {\n    validator.assertOptions(transitional, {\n      silentJSONParsing: validators.transitional(validators.boolean),\n      forcedJSONParsing: validators.transitional(validators.boolean),\n      clarifyTimeoutError: validators.transitional(validators.boolean)\n    }, false);\n  }\n\n  // filter out skipped interceptors\n  var requestInterceptorChain = [];\n  var synchronousRequestInterceptors = true;\n  this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {\n    if (typeof interceptor.runWhen === 'function' && interceptor.runWhen(config) === false) {\n      return;\n    }\n\n    synchronousRequestInterceptors = synchronousRequestInterceptors && interceptor.synchronous;\n\n    requestInterceptorChain.unshift(interceptor.fulfilled, interceptor.rejected);\n  });\n\n  var responseInterceptorChain = [];\n  this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {\n    responseInterceptorChain.push(interceptor.fulfilled, interceptor.rejected);\n  });\n\n  var promise;\n\n  if (!synchronousRequestInterceptors) {\n    var chain = [dispatchRequest, undefined];\n\n    Array.prototype.unshift.apply(chain, requestInterceptorChain);\n    chain = chain.concat(responseInterceptorChain);\n\n    promise = Promise.resolve(config);\n    while (chain.length) {\n      promise = promise.then(chain.shift(), chain.shift());\n    }\n\n    return promise;\n  }\n\n\n  var newConfig = config;\n  while (requestInterceptorChain.length) {\n    var onFulfilled = requestInterceptorChain.shift();\n    var onRejected = requestInterceptorChain.shift();\n    try {\n      newConfig = onFulfilled(newConfig);\n    } catch (error) {\n      onRejected(error);\n      break;\n    }\n  }\n\n  try {\n    promise = dispatchRequest(newConfig);\n  } catch (error) {\n    return Promise.reject(error);\n  }\n\n  while (responseInterceptorChain.length) {\n    promise = promise.then(responseInterceptorChain.shift(), responseInterceptorChain.shift());\n  }\n\n  return promise;\n};\n\nAxios.prototype.getUri = function getUri(config) {\n  config = mergeConfig(this.defaults, config);\n  var fullPath = buildFullPath(config.baseURL, config.url);\n  return buildURL(fullPath, config.params, config.paramsSerializer);\n};\n\n// Provide aliases for supported request methods\nutils.forEach(['delete', 'get', 'head', 'options'], function forEachMethodNoData(method) {\n  /*eslint func-names:0*/\n  Axios.prototype[method] = function(url, config) {\n    return this.request(mergeConfig(config || {}, {\n      method: method,\n      url: url,\n      data: (config || {}).data\n    }));\n  };\n});\n\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  /*eslint func-names:0*/\n\n  function generateHTTPMethod(isForm) {\n    return function httpMethod(url, data, config) {\n      return this.request(mergeConfig(config || {}, {\n        method: method,\n        headers: isForm ? {\n          'Content-Type': 'multipart/form-data'\n        } : {},\n        url: url,\n        data: data\n      }));\n    };\n  }\n\n  Axios.prototype[method] = generateHTTPMethod();\n\n  Axios.prototype[method + 'Form'] = generateHTTPMethod(true);\n});\n\nmodule.exports = Axios;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AACjC,IAAIC,QAAQ,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAC7C,IAAIE,kBAAkB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AACxD,IAAIG,eAAe,GAAGH,OAAO,CAAC,mBAAmB,CAAC;AAClD,IAAII,WAAW,GAAGJ,OAAO,CAAC,eAAe,CAAC;AAC1C,IAAIK,aAAa,GAAGL,OAAO,CAAC,iBAAiB,CAAC;AAC9C,IAAIM,SAAS,GAAGN,OAAO,CAAC,sBAAsB,CAAC;AAE/C,IAAIO,UAAU,GAAGD,SAAS,CAACC,UAAU;AACrC;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,cAAc,EAAE;EAC7B,IAAI,CAACC,QAAQ,GAAGD,cAAc;EAC9B,IAAI,CAACE,YAAY,GAAG;IAClBC,OAAO,EAAE,IAAIV,kBAAkB,CAAC,CAAC;IACjCW,QAAQ,EAAE,IAAIX,kBAAkB,CAAC;EACnC,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACAM,KAAK,CAACM,SAAS,CAACF,OAAO,GAAG,SAASA,OAAOA,CAACG,WAAW,EAAEC,MAAM,EAAE;EAC9D;EACA;EACA,IAAI,OAAOD,WAAW,KAAK,QAAQ,EAAE;IACnCC,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;IACrBA,MAAM,CAACC,GAAG,GAAGF,WAAW;EAC1B,CAAC,MAAM;IACLC,MAAM,GAAGD,WAAW,IAAI,CAAC,CAAC;EAC5B;EAEAC,MAAM,GAAGZ,WAAW,CAAC,IAAI,CAACM,QAAQ,EAAEM,MAAM,CAAC;;EAE3C;EACA,IAAIA,MAAM,CAACE,MAAM,EAAE;IACjBF,MAAM,CAACE,MAAM,GAAGF,MAAM,CAACE,MAAM,CAACC,WAAW,CAAC,CAAC;EAC7C,CAAC,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACQ,MAAM,EAAE;IAC/BF,MAAM,CAACE,MAAM,GAAG,IAAI,CAACR,QAAQ,CAACQ,MAAM,CAACC,WAAW,CAAC,CAAC;EACpD,CAAC,MAAM;IACLH,MAAM,CAACE,MAAM,GAAG,KAAK;EACvB;EAEA,IAAIE,YAAY,GAAGJ,MAAM,CAACI,YAAY;EAEtC,IAAIA,YAAY,KAAKC,SAAS,EAAE;IAC9Bf,SAAS,CAACgB,aAAa,CAACF,YAAY,EAAE;MACpCG,iBAAiB,EAAEhB,UAAU,CAACa,YAAY,CAACb,UAAU,CAACiB,OAAO,CAAC;MAC9DC,iBAAiB,EAAElB,UAAU,CAACa,YAAY,CAACb,UAAU,CAACiB,OAAO,CAAC;MAC9DE,mBAAmB,EAAEnB,UAAU,CAACa,YAAY,CAACb,UAAU,CAACiB,OAAO;IACjE,CAAC,EAAE,KAAK,CAAC;EACX;;EAEA;EACA,IAAIG,uBAAuB,GAAG,EAAE;EAChC,IAAIC,8BAA8B,GAAG,IAAI;EACzC,IAAI,CAACjB,YAAY,CAACC,OAAO,CAACiB,OAAO,CAAC,SAASC,0BAA0BA,CAACC,WAAW,EAAE;IACjF,IAAI,OAAOA,WAAW,CAACC,OAAO,KAAK,UAAU,IAAID,WAAW,CAACC,OAAO,CAAChB,MAAM,CAAC,KAAK,KAAK,EAAE;MACtF;IACF;IAEAY,8BAA8B,GAAGA,8BAA8B,IAAIG,WAAW,CAACE,WAAW;IAE1FN,uBAAuB,CAACO,OAAO,CAACH,WAAW,CAACI,SAAS,EAAEJ,WAAW,CAACK,QAAQ,CAAC;EAC9E,CAAC,CAAC;EAEF,IAAIC,wBAAwB,GAAG,EAAE;EACjC,IAAI,CAAC1B,YAAY,CAACE,QAAQ,CAACgB,OAAO,CAAC,SAASS,wBAAwBA,CAACP,WAAW,EAAE;IAChFM,wBAAwB,CAACE,IAAI,CAACR,WAAW,CAACI,SAAS,EAAEJ,WAAW,CAACK,QAAQ,CAAC;EAC5E,CAAC,CAAC;EAEF,IAAII,OAAO;EAEX,IAAI,CAACZ,8BAA8B,EAAE;IACnC,IAAIa,KAAK,GAAG,CAACtC,eAAe,EAAEkB,SAAS,CAAC;IAExCqB,KAAK,CAAC5B,SAAS,CAACoB,OAAO,CAACS,KAAK,CAACF,KAAK,EAAEd,uBAAuB,CAAC;IAC7Dc,KAAK,GAAGA,KAAK,CAACG,MAAM,CAACP,wBAAwB,CAAC;IAE9CG,OAAO,GAAGK,OAAO,CAACC,OAAO,CAAC9B,MAAM,CAAC;IACjC,OAAOyB,KAAK,CAACM,MAAM,EAAE;MACnBP,OAAO,GAAGA,OAAO,CAACQ,IAAI,CAACP,KAAK,CAACQ,KAAK,CAAC,CAAC,EAAER,KAAK,CAACQ,KAAK,CAAC,CAAC,CAAC;IACtD;IAEA,OAAOT,OAAO;EAChB;EAGA,IAAIU,SAAS,GAAGlC,MAAM;EACtB,OAAOW,uBAAuB,CAACoB,MAAM,EAAE;IACrC,IAAII,WAAW,GAAGxB,uBAAuB,CAACsB,KAAK,CAAC,CAAC;IACjD,IAAIG,UAAU,GAAGzB,uBAAuB,CAACsB,KAAK,CAAC,CAAC;IAChD,IAAI;MACFC,SAAS,GAAGC,WAAW,CAACD,SAAS,CAAC;IACpC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdD,UAAU,CAACC,KAAK,CAAC;MACjB;IACF;EACF;EAEA,IAAI;IACFb,OAAO,GAAGrC,eAAe,CAAC+C,SAAS,CAAC;EACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,OAAOR,OAAO,CAACS,MAAM,CAACD,KAAK,CAAC;EAC9B;EAEA,OAAOhB,wBAAwB,CAACU,MAAM,EAAE;IACtCP,OAAO,GAAGA,OAAO,CAACQ,IAAI,CAACX,wBAAwB,CAACY,KAAK,CAAC,CAAC,EAAEZ,wBAAwB,CAACY,KAAK,CAAC,CAAC,CAAC;EAC5F;EAEA,OAAOT,OAAO;AAChB,CAAC;AAEDhC,KAAK,CAACM,SAAS,CAACyC,MAAM,GAAG,SAASA,MAAMA,CAACvC,MAAM,EAAE;EAC/CA,MAAM,GAAGZ,WAAW,CAAC,IAAI,CAACM,QAAQ,EAAEM,MAAM,CAAC;EAC3C,IAAIwC,QAAQ,GAAGnD,aAAa,CAACW,MAAM,CAACyC,OAAO,EAAEzC,MAAM,CAACC,GAAG,CAAC;EACxD,OAAOhB,QAAQ,CAACuD,QAAQ,EAAExC,MAAM,CAAC0C,MAAM,EAAE1C,MAAM,CAAC2C,gBAAgB,CAAC;AACnE,CAAC;;AAED;AACA5D,KAAK,CAAC8B,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,SAAS+B,mBAAmBA,CAAC1C,MAAM,EAAE;EACvF;EACAV,KAAK,CAACM,SAAS,CAACI,MAAM,CAAC,GAAG,UAASD,GAAG,EAAED,MAAM,EAAE;IAC9C,OAAO,IAAI,CAACJ,OAAO,CAACR,WAAW,CAACY,MAAM,IAAI,CAAC,CAAC,EAAE;MAC5CE,MAAM,EAAEA,MAAM;MACdD,GAAG,EAAEA,GAAG;MACR4C,IAAI,EAAE,CAAC7C,MAAM,IAAI,CAAC,CAAC,EAAE6C;IACvB,CAAC,CAAC,CAAC;EACL,CAAC;AACH,CAAC,CAAC;AAEF9D,KAAK,CAAC8B,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,SAASiC,qBAAqBA,CAAC5C,MAAM,EAAE;EAC7E;;EAEA,SAAS6C,kBAAkBA,CAACC,MAAM,EAAE;IAClC,OAAO,SAASC,UAAUA,CAAChD,GAAG,EAAE4C,IAAI,EAAE7C,MAAM,EAAE;MAC5C,OAAO,IAAI,CAACJ,OAAO,CAACR,WAAW,CAACY,MAAM,IAAI,CAAC,CAAC,EAAE;QAC5CE,MAAM,EAAEA,MAAM;QACdgD,OAAO,EAAEF,MAAM,GAAG;UAChB,cAAc,EAAE;QAClB,CAAC,GAAG,CAAC,CAAC;QACN/C,GAAG,EAAEA,GAAG;QACR4C,IAAI,EAAEA;MACR,CAAC,CAAC,CAAC;IACL,CAAC;EACH;EAEArD,KAAK,CAACM,SAAS,CAACI,MAAM,CAAC,GAAG6C,kBAAkB,CAAC,CAAC;EAE9CvD,KAAK,CAACM,SAAS,CAACI,MAAM,GAAG,MAAM,CAAC,GAAG6C,kBAAkB,CAAC,IAAI,CAAC;AAC7D,CAAC,CAAC;AAEFI,MAAM,CAACC,OAAO,GAAG5D,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}