{"ast": null, "code": "import axios from 'axios';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: 'http://localhost:8000/api',\n  withCredentials: true,\n  // Important for session-based auth\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add request interceptor to handle CSRF token\napi.interceptors.request.use(config => {\n  // Get CSRF token from cookie\n  const csrfToken = getCookie('csrftoken');\n  if (csrfToken) {\n    config.headers['X-CSRFToken'] = csrfToken;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Add response interceptor to handle errors\napi.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Handle unauthorized access\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// Helper function to get cookie value\nfunction getCookie(name) {\n  let cookieValue = null;\n  if (document.cookie && document.cookie !== '') {\n    const cookies = document.cookie.split(';');\n    for (let i = 0; i < cookies.length; i++) {\n      const cookie = cookies[i].trim();\n      if (cookie.substring(0, name.length + 1) === name + '=') {\n        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));\n        break;\n      }\n    }\n  }\n  return cookieValue;\n}\n\n// Auth API calls\nexport const authAPI = {\n  login: credentials => api.post('/auth/login/', credentials),\n  register: userData => api.post('/auth/register/', userData),\n  logout: () => api.post('/auth/logout/'),\n  getCurrentUser: () => api.get('/auth/current-user/'),\n  getDashboard: () => api.get('/auth/dashboard/')\n};\n\n// Home API calls\nexport const homeAPI = {\n  getHomeData: () => api.get('/auth/home/')\n};\n\n// Course API calls\nexport const courseAPI = {\n  getCourses: params => api.get('/courses/', {\n    params\n  }),\n  getCourseDetail: courseId => api.get(`/courses/${courseId}/`),\n  enrollCourse: courseId => api.post(`/courses/${courseId}/enroll/`),\n  getLessonDetail: (courseId, lessonId) => api.get(`/courses/${courseId}/lessons/${lessonId}/`),\n  markLessonComplete: data => api.post('/courses/lessons/complete/', data)\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "api", "create", "baseURL", "withCredentials", "headers", "interceptors", "request", "use", "config", "csrfToken", "<PERSON><PERSON><PERSON><PERSON>", "error", "Promise", "reject", "response", "_error$response", "status", "window", "location", "href", "name", "cookieValue", "document", "cookie", "cookies", "split", "i", "length", "trim", "substring", "decodeURIComponent", "authAPI", "login", "credentials", "post", "register", "userData", "logout", "getCurrentUser", "get", "getDashboard", "homeAPI", "getHomeData", "courseAPI", "getCourses", "params", "getCourseDetail", "courseId", "enrollCourse", "getLessonDetail", "lessonId", "markLessonComplete", "data"], "sources": ["E:/Downloads/lms_backend/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: 'http://localhost:8000/api',\n  withCredentials: true, // Important for session-based auth\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add request interceptor to handle CSRF token\napi.interceptors.request.use(\n  (config) => {\n    // Get CSRF token from cookie\n    const csrfToken = getCookie('csrftoken');\n    if (csrfToken) {\n      config.headers['X-CSRFToken'] = csrfToken;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Add response interceptor to handle errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      // Handle unauthorized access\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Helper function to get cookie value\nfunction getCookie(name) {\n  let cookieValue = null;\n  if (document.cookie && document.cookie !== '') {\n    const cookies = document.cookie.split(';');\n    for (let i = 0; i < cookies.length; i++) {\n      const cookie = cookies[i].trim();\n      if (cookie.substring(0, name.length + 1) === (name + '=')) {\n        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));\n        break;\n      }\n    }\n  }\n  return cookieValue;\n}\n\n// Auth API calls\nexport const authAPI = {\n  login: (credentials) => api.post('/auth/login/', credentials),\n  register: (userData) => api.post('/auth/register/', userData),\n  logout: () => api.post('/auth/logout/'),\n  getCurrentUser: () => api.get('/auth/current-user/'),\n  getDashboard: () => api.get('/auth/dashboard/'),\n};\n\n// Home API calls\nexport const homeAPI = {\n  getHomeData: () => api.get('/auth/home/'),\n};\n\n// Course API calls\nexport const courseAPI = {\n  getCourses: (params) => api.get('/courses/', { params }),\n  getCourseDetail: (courseId) => api.get(`/courses/${courseId}/`),\n  enrollCourse: (courseId) => api.post(`/courses/${courseId}/enroll/`),\n  getLessonDetail: (courseId, lessonId) => api.get(`/courses/${courseId}/lessons/${lessonId}/`),\n  markLessonComplete: (data) => api.post('/courses/lessons/complete/', data),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,GAAG,GAAGD,KAAK,CAACE,MAAM,CAAC;EACvBC,OAAO,EAAE,2BAA2B;EACpCC,eAAe,EAAE,IAAI;EAAE;EACvBC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV;EACA,MAAMC,SAAS,GAAGC,SAAS,CAAC,WAAW,CAAC;EACxC,IAAID,SAAS,EAAE;IACbD,MAAM,CAACJ,OAAO,CAAC,aAAa,CAAC,GAAGK,SAAS;EAC3C;EACA,OAAOD,MAAM;AACf,CAAC,EACAG,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAX,GAAG,CAACK,YAAY,CAACS,QAAQ,CAACP,GAAG,CAC1BO,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOP,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,SAASD,SAASA,CAACU,IAAI,EAAE;EACvB,IAAIC,WAAW,GAAG,IAAI;EACtB,IAAIC,QAAQ,CAACC,MAAM,IAAID,QAAQ,CAACC,MAAM,KAAK,EAAE,EAAE;IAC7C,MAAMC,OAAO,GAAGF,QAAQ,CAACC,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACvC,MAAMH,MAAM,GAAGC,OAAO,CAACE,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC;MAChC,IAAIL,MAAM,CAACM,SAAS,CAAC,CAAC,EAAET,IAAI,CAACO,MAAM,GAAG,CAAC,CAAC,KAAMP,IAAI,GAAG,GAAI,EAAE;QACzDC,WAAW,GAAGS,kBAAkB,CAACP,MAAM,CAACM,SAAS,CAACT,IAAI,CAACO,MAAM,GAAG,CAAC,CAAC,CAAC;QACnE;MACF;IACF;EACF;EACA,OAAON,WAAW;AACpB;;AAEA;AACA,OAAO,MAAMU,OAAO,GAAG;EACrBC,KAAK,EAAGC,WAAW,IAAKjC,GAAG,CAACkC,IAAI,CAAC,cAAc,EAAED,WAAW,CAAC;EAC7DE,QAAQ,EAAGC,QAAQ,IAAKpC,GAAG,CAACkC,IAAI,CAAC,iBAAiB,EAAEE,QAAQ,CAAC;EAC7DC,MAAM,EAAEA,CAAA,KAAMrC,GAAG,CAACkC,IAAI,CAAC,eAAe,CAAC;EACvCI,cAAc,EAAEA,CAAA,KAAMtC,GAAG,CAACuC,GAAG,CAAC,qBAAqB,CAAC;EACpDC,YAAY,EAAEA,CAAA,KAAMxC,GAAG,CAACuC,GAAG,CAAC,kBAAkB;AAChD,CAAC;;AAED;AACA,OAAO,MAAME,OAAO,GAAG;EACrBC,WAAW,EAAEA,CAAA,KAAM1C,GAAG,CAACuC,GAAG,CAAC,aAAa;AAC1C,CAAC;;AAED;AACA,OAAO,MAAMI,SAAS,GAAG;EACvBC,UAAU,EAAGC,MAAM,IAAK7C,GAAG,CAACuC,GAAG,CAAC,WAAW,EAAE;IAAEM;EAAO,CAAC,CAAC;EACxDC,eAAe,EAAGC,QAAQ,IAAK/C,GAAG,CAACuC,GAAG,CAAC,YAAYQ,QAAQ,GAAG,CAAC;EAC/DC,YAAY,EAAGD,QAAQ,IAAK/C,GAAG,CAACkC,IAAI,CAAC,YAAYa,QAAQ,UAAU,CAAC;EACpEE,eAAe,EAAEA,CAACF,QAAQ,EAAEG,QAAQ,KAAKlD,GAAG,CAACuC,GAAG,CAAC,YAAYQ,QAAQ,YAAYG,QAAQ,GAAG,CAAC;EAC7FC,kBAAkB,EAAGC,IAAI,IAAKpD,GAAG,CAACkC,IAAI,CAAC,4BAA4B,EAAEkB,IAAI;AAC3E,CAAC;AAED,eAAepD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}