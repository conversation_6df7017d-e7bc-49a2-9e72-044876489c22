import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { courseAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

const CourseDetail = () => {
  const { courseId } = useParams();
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [courseData, setCourseData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [enrolling, setEnrolling] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadCourseDetail();
  }, [courseId]);

  const loadCourseDetail = async () => {
    try {
      setLoading(true);
      const response = await courseAPI.getCourseDetail(courseId);
      setCourseData(response.data);
    } catch (error) {
      console.error('Failed to load course detail:', error);
      setError('Failed to load course details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleEnroll = async () => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    if (user?.role !== 'student') {
      alert('Only students can enroll in courses');
      return;
    }

    try {
      setEnrolling(true);
      const response = await courseAPI.enrollCourse(courseId);
      alert(response.data.message);
      // Reload course data to update enrollment status
      loadCourseDetail();
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to enroll in course';
      alert(errorMessage);
    } finally {
      setEnrolling(false);
    }
  };

  const formatPrice = (price) => {
    return price === 0 ? 'Free' : `$${price}`;
  };

  const getUserAvatar = (user, size = 48) => {
    if (!user) return null;

    const initials = user.first_name && user.last_name
      ? `${user.first_name[0]}${user.last_name[0]}`.toUpperCase()
      : user.username[0].toUpperCase();

    return (
      <div
        className="user-avatar d-flex align-items-center justify-content-center text-white rounded-circle"
        style={{ width: size, height: size, fontSize: size * 0.4 }}
      >
        {initials}
      </div>
    );
  };

  const getCourseImage = (course) => {
    if (course.banner) {
      return course.banner;
    }

    // Default placeholder for courses without images
    const defaultCourseImage = '/api/placeholder/800/400';

    return defaultCourseImage;
  };

  if (loading) {
    return (
      <div className="container my-5">
        <div className="d-flex justify-content-center align-items-center" style={{ height: '50vh' }}>
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container my-5">
        <div className="alert alert-danger" role="alert">
          <h4 className="alert-heading">Error</h4>
          <p>{error}</p>
          <hr />
          <Link to="/courses" className="btn btn-primary">Back to Courses</Link>
        </div>
      </div>
    );
  }

  if (!courseData) {
    return (
      <div className="container my-5">
        <div className="alert alert-warning" role="alert">
          Course not found.
          <Link to="/courses" className="btn btn-primary ms-3">Back to Courses</Link>
        </div>
      </div>
    );
  }

  const { course, is_enrolled, enrollment, can_access_content } = courseData;

  return (
    <div className="container my-5">
      {/* Breadcrumb */}
      <nav aria-label="breadcrumb" className="mb-4">
        <ol className="breadcrumb">
          <li className="breadcrumb-item"><Link to="/">Home</Link></li>
          <li className="breadcrumb-item"><Link to="/courses">Courses</Link></li>
          <li className="breadcrumb-item active" aria-current="page">{course.title}</li>
        </ol>
      </nav>

      <div className="row">
        {/* Main Content */}
        <div className="col-lg-8">
          {/* Course Header */}
          <div className="card mb-4">
            <img
              src={getCourseImage(course)}
              className="card-img-top"
              alt={course.title}
              style={{ height: '300px', objectFit: 'cover' }}
            />
            <div className="card-body">
              <div className="d-flex justify-content-between align-items-start mb-3">
                <div>
                  <span className="badge badge-category mb-2">{course.category?.title}</span>
                  <h1 className="card-title h3">{course.title}</h1>
                </div>
                <div className="text-end">
                  <div className="rating mb-2">
                    {[1,2,3,4,5].map(star => (
                      <i key={star} className="fas fa-star text-warning"></i>
                    ))}
                    <span className="ms-1 text-dark">5.0 (128 reviews)</span>
                  </div>
                  <p className="h4 text-primary mb-0">{formatPrice(course.price)}</p>
                </div>
              </div>

              <p className="card-text">{course.description}</p>

              {/* Course Stats */}
              <div className="row text-center mb-4">
                <div className="col-md-3">
                  <div className="border-end">
                    <h5 className="text-primary">{course.total_lessons}</h5>
                    <small className="text-muted">Lessons</small>
                  </div>
                </div>
                <div className="col-md-3">
                  <div className="border-end">
                    <h5 className="text-primary">{course.duration}h</h5>
                    <small className="text-muted">Duration</small>
                  </div>
                </div>
                <div className="col-md-3">
                  <div className="border-end">
                    <h5 className="text-primary">{course.total_students}</h5>
                    <small className="text-muted">Students</small>
                  </div>
                </div>
                <div className="col-md-3">
                  <h5 className="text-primary">Beginner</h5>
                  <small className="text-muted">Level</small>
                </div>
              </div>

              {/* Enrollment Status */}
              {is_enrolled ? (
                <div className="alert alert-success" role="alert">
                  <i className="fas fa-check-circle me-2"></i>
                  You are enrolled in this course! 
                  {enrollment && (
                    <span className="ms-2">Progress: {enrollment.progress}%</span>
                  )}
                </div>
              ) : (
                <div className="d-grid gap-2 d-md-flex">
                  <button 
                    className="btn btn-primary btn-lg me-md-2" 
                    onClick={handleEnroll}
                    disabled={enrolling}
                  >
                    {enrolling ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Enrolling...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-play me-2"></i>
                        {course.price === 0 ? 'Enroll for Free' : `Enroll for ${formatPrice(course.price)}`}
                      </>
                    )}
                  </button>
                  <button className="btn btn-outline-secondary btn-lg">
                    <i className="fas fa-heart me-2"></i>Add to Wishlist
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Course Content */}
          {can_access_content && course.lessons && course.lessons.length > 0 && (
            <div className="card mb-4">
              <div className="card-header">
                <h5 className="mb-0">Course Content</h5>
              </div>
              <div className="card-body">
                <div className="accordion" id="lessonsAccordion">
                  {course.lessons.map((lesson, index) => (
                    <div key={lesson.id} className="accordion-item">
                      <h2 className="accordion-header" id={`heading${lesson.id}`}>
                        <button 
                          className="accordion-button collapsed" 
                          type="button" 
                          data-bs-toggle="collapse" 
                          data-bs-target={`#collapse${lesson.id}`}
                          aria-expanded="false" 
                          aria-controls={`collapse${lesson.id}`}
                        >
                          <span className="me-3">
                            <i className={`fas ${lesson.lesson_type === 'video' ? 'fa-play-circle' : 'fa-file-text'} text-primary`}></i>
                          </span>
                          <div className="flex-grow-1">
                            <div className="fw-medium">{lesson.title}</div>
                            <small className="text-muted">{lesson.duration_minutes} minutes</small>
                          </div>
                        </button>
                      </h2>
                      <div 
                        id={`collapse${lesson.id}`} 
                        className="accordion-collapse collapse" 
                        aria-labelledby={`heading${lesson.id}`}
                        data-bs-parent="#lessonsAccordion"
                      >
                        <div className="accordion-body">
                          <p>{lesson.description}</p>
                          {is_enrolled && (
                            <Link 
                              to={`/courses/${courseId}/lessons/${lesson.id}`}
                              className="btn btn-sm btn-primary"
                            >
                              Start Lesson
                            </Link>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Course Materials */}
          {can_access_content && course.materials && course.materials.length > 0 && (
            <div className="card mb-4">
              <div className="card-header">
                <h5 className="mb-0">Course Materials</h5>
              </div>
              <div className="card-body">
                <div className="list-group list-group-flush">
                  {course.materials.map(material => (
                    <div key={material.id} className="list-group-item d-flex justify-content-between align-items-center">
                      <div>
                        <i className={`fas ${material.file_type === 'pdf' ? 'fa-file-pdf' : 'fa-file'} text-danger me-2`}></i>
                        <strong>{material.title}</strong>
                        <p className="mb-0 text-muted small">{material.description}</p>
                      </div>
                      {material.file && (
                        <a href={material.file} target="_blank" rel="noopener noreferrer" className="btn btn-sm btn-outline-primary">
                          <i className="fas fa-download me-1"></i>Download
                        </a>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="col-lg-4">
          {/* Instructor Info */}
          <div className="card mb-4">
            <div className="card-header">
              <h5 className="mb-0">Instructor</h5>
            </div>
            <div className="card-body text-center">
              {getUserAvatar(course.instructor, 80)}
              <h6 className="mt-3 mb-1">
                {course.instructor?.first_name && course.instructor?.last_name 
                  ? `${course.instructor.first_name} ${course.instructor.last_name}`
                  : course.instructor?.username}
              </h6>
              <p className="text-muted small">Course Instructor</p>
              <div className="row text-center">
                <div className="col-6">
                  <h6 className="text-primary">4.8</h6>
                  <small className="text-muted">Rating</small>
                </div>
                <div className="col-6">
                  <h6 className="text-primary">1,234</h6>
                  <small className="text-muted">Students</small>
                </div>
              </div>
            </div>
          </div>

          {/* Course Features */}
          <div className="card mb-4">
            <div className="card-header">
              <h5 className="mb-0">This Course Includes</h5>
            </div>
            <div className="card-body">
              <ul className="list-unstyled">
                <li className="mb-2">
                  <i className="fas fa-video text-primary me-2"></i>
                  {course.total_lessons} video lessons
                </li>
                <li className="mb-2">
                  <i className="fas fa-clock text-primary me-2"></i>
                  {course.duration} hours of content
                </li>
                <li className="mb-2">
                  <i className="fas fa-file-download text-primary me-2"></i>
                  Downloadable resources
                </li>
                <li className="mb-2">
                  <i className="fas fa-mobile-alt text-primary me-2"></i>
                  Access on mobile and desktop
                </li>
                <li className="mb-2">
                  <i className="fas fa-certificate text-primary me-2"></i>
                  Certificate of completion
                </li>
                <li className="mb-2">
                  <i className="fas fa-infinity text-primary me-2"></i>
                  Lifetime access
                </li>
              </ul>
            </div>
          </div>

          {/* Related Courses */}
          <div className="card">
            <div className="card-header">
              <h5 className="mb-0">Related Courses</h5>
            </div>
            <div className="card-body">
              <p className="text-muted">More courses from this category coming soon!</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseDetail;
