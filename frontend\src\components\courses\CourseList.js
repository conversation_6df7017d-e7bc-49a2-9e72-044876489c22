import React, { useState, useEffect } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { courseAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

const CourseList = () => {
  const { user, isAuthenticated } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const [courses, setCourses] = useState([]);
  const [categories, setCategories] = useState([]);
  const [pagination, setPagination] = useState({});
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '');
  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || '');

  useEffect(() => {
    loadCourses();
  }, [searchParams]);

  const loadCourses = async () => {
    try {
      setLoading(true);
      const params = Object.fromEntries(searchParams);
      const response = await courseAPI.getCourses(params);
      setCourses(response.data.courses);
      setCategories(response.data.categories);
      setPagination(response.data.pagination);
    } catch (error) {
      console.error('Failed to load courses:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    const newParams = new URLSearchParams(searchParams);
    if (searchQuery.trim()) {
      newParams.set('search', searchQuery.trim());
    } else {
      newParams.delete('search');
    }
    newParams.delete('page'); // Reset to first page
    setSearchParams(newParams);
  };

  const handleCategoryFilter = (categoryId) => {
    const newParams = new URLSearchParams(searchParams);
    if (categoryId) {
      newParams.set('category', categoryId);
    } else {
      newParams.delete('category');
    }
    newParams.delete('page'); // Reset to first page
    setSearchParams(newParams);
    setSelectedCategory(categoryId);
  };

  const handlePageChange = (page) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set('page', page);
    setSearchParams(newParams);
  };

  const formatPrice = (price) => {
    return price === 0 ? 'Free' : `$${price}`;
  };

  const getUserAvatar = (user, size = 36) => {
    if (!user) return null;
    
    const initials = user.first_name && user.last_name 
      ? `${user.first_name[0]}${user.last_name[0]}`.toUpperCase()
      : user.username[0].toUpperCase();
    
    return (
      <div 
        className="user-avatar d-flex align-items-center justify-content-center text-white rounded-circle"
        style={{ width: size, height: size, fontSize: size * 0.4 }}
      >
        {initials}
      </div>
    );
  };

  const truncateWords = (text, wordLimit) => {
    if (!text) return '';
    const words = text.split(' ');
    if (words.length <= wordLimit) return text;
    return words.slice(0, wordLimit).join(' ') + '...';
  };

  const getCourseImage = (course) => {
    if (course.banner) {
      return course.banner;
    }

    // Default placeholder for courses without images
    const defaultCourseImage = '/api/placeholder/400/240';

    return defaultCourseImage;
  };

  if (loading) {
    return (
      <div className="container my-5">
        <div className="d-flex justify-content-center align-items-center" style={{ height: '50vh' }}>
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container my-5">
      {/* Header */}
      <div className="row mb-4">
        <div className="col-md-8">
          <h2>All Courses</h2>
          <p className="text-muted">Discover and learn from our extensive course catalog</p>
        </div>
        <div className="col-md-4">
          <form onSubmit={handleSearch}>
            <div className="input-group">
              <input 
                type="text" 
                className="form-control" 
                placeholder="Search courses..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <button className="btn btn-outline-primary" type="submit">
                <i className="fas fa-search"></i>
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Category Filter */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex flex-wrap">
            <button 
              className={`category-pill ${!selectedCategory ? 'active' : 'bg-light'} border-0 me-2 mb-2`}
              onClick={() => handleCategoryFilter('')}
            >
              All Categories
            </button>
            {categories.map(category => (
              <button 
                key={category.id}
                className={`category-pill ${selectedCategory === category.id.toString() ? 'active' : 'bg-light'} border-0 me-2 mb-2`}
                onClick={() => handleCategoryFilter(category.id)}
              >
                {category.title}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Course Grid */}
      <div className="row g-4 mb-5">
        {courses.length > 0 ? (
          courses.map(course => (
            <div key={course.id} className="col-md-6 col-lg-4">
              <div className="card course-card h-100">
                <img
                  src={getCourseImage(course)}
                  className="card-img-top"
                  alt={course.title}
                />
                <div className="card-body">
                  <div className="d-flex justify-content-between align-items-center mb-2">
                    <span className="badge badge-category">{course.category?.title}</span>
                    <div className="rating">
                      {[1,2,3,4,5].map(star => (
                        <i key={star} className="fas fa-star"></i>
                      ))}
                      <span className="ms-1 text-dark">5.0</span>
                    </div>
                  </div>
                  <h5 className="card-title">
                    <Link to={`/courses/${course.id}`} className="text-decoration-none text-dark">
                      {truncateWords(course.title, 8)}
                    </Link>
                  </h5>
                  <p className="card-text">{truncateWords(course.description, 15)}</p>
                  <div className="course-instructor mb-3">
                    {getUserAvatar(course.instructor, 36)}
                    <div className="ms-2">
                      <small className="text-muted">Instructor</small>
                      <p className="mb-0 fw-medium">
                        {course.instructor?.first_name && course.instructor?.last_name 
                          ? `${course.instructor.first_name} ${course.instructor.last_name}`
                          : course.instructor?.username}
                      </p>
                    </div>
                  </div>
                  <div className="d-flex justify-content-between align-items-center">
                    <span className="fw-bold text-primary">{formatPrice(course.price)}</span>
                    <Link to={`/courses/${course.id}`} className="btn btn-sm btn-primary">
                      View Course
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="col-12">
            <div className="text-center py-5">
              <i className="fas fa-search fa-3x text-muted mb-3"></i>
              <h4>No courses found</h4>
              <p className="text-muted">Try adjusting your search criteria or browse all courses.</p>
              <button 
                className="btn btn-primary"
                onClick={() => {
                  setSearchParams({});
                  setSearchQuery('');
                  setSelectedCategory('');
                }}
              >
                View All Courses
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Pagination */}
      {pagination.total_pages > 1 && (
        <div className="row">
          <div className="col-12">
            <nav aria-label="Course pagination">
              <ul className="pagination justify-content-center">
                <li className={`page-item ${!pagination.has_previous ? 'disabled' : ''}`}>
                  <button 
                    className="page-link"
                    onClick={() => handlePageChange(pagination.current_page - 1)}
                    disabled={!pagination.has_previous}
                  >
                    Previous
                  </button>
                </li>
                
                {[...Array(pagination.total_pages)].map((_, index) => {
                  const page = index + 1;
                  return (
                    <li key={page} className={`page-item ${pagination.current_page === page ? 'active' : ''}`}>
                      <button 
                        className="page-link"
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </button>
                    </li>
                  );
                })}
                
                <li className={`page-item ${!pagination.has_next ? 'disabled' : ''}`}>
                  <button 
                    className="page-link"
                    onClick={() => handlePageChange(pagination.current_page + 1)}
                    disabled={!pagination.has_next}
                  >
                    Next
                  </button>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      )}
    </div>
  );
};

export default CourseList;
