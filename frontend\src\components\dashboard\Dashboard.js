import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { authAPI } from '../../services/api';

const Dashboard = () => {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      const response = await authAPI.getDashboard();
      setDashboardData(response.data);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price) => {
    return price === 0 ? 'Free' : `$${price}`;
  };

  const getUserAvatar = (user, size = 48) => {
    if (!user) return null;

    const initials = user.first_name && user.last_name
      ? `${user.first_name[0]}${user.last_name[0]}`.toUpperCase()
      : user.username[0].toUpperCase();

    return (
      <div
        className="user-avatar d-flex align-items-center justify-content-center text-white rounded-circle"
        style={{ width: size, height: size, fontSize: size * 0.4 }}
      >
        {initials}
      </div>
    );
  };

  const getCourseImage = (course) => {
    if (course.banner) {
      return course.banner;
    }

    // Default placeholder for courses without images
    const defaultCourseImage = '/api/placeholder/400/240';

    return defaultCourseImage;
  };

  if (loading) {
    return (
      <div className="container my-5">
        <div className="d-flex justify-content-center align-items-center" style={{ height: '50vh' }}>
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="container my-5">
        <div className="alert alert-danger" role="alert">
          Failed to load dashboard data. Please try refreshing the page.
        </div>
      </div>
    );
  }

  return (
    <div className="container my-5">
      {/* Welcome Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex align-items-center mb-3">
            {getUserAvatar(user, 64)}
            <div className="ms-3">
              <h2 className="mb-1">
                Welcome back, {user?.first_name || user?.username}!
              </h2>
              <p className="text-muted mb-0">
                {user?.role === 'teacher' ? 'Teacher Dashboard' : 'Student Dashboard'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="row g-4 mb-5">
        {user?.role === 'teacher' ? (
          <>
            <div className="col-md-3">
              <div className="card text-center">
                <div className="card-body">
                  <i className="fas fa-chalkboard fa-2x text-primary mb-3"></i>
                  <h3 className="text-primary">{dashboardData.total_courses || 0}</h3>
                  <p className="text-muted mb-0">Total Courses</p>
                </div>
              </div>
            </div>
            <div className="col-md-3">
              <div className="card text-center">
                <div className="card-body">
                  <i className="fas fa-users fa-2x text-success mb-3"></i>
                  <h3 className="text-success">{dashboardData.total_students || 0}</h3>
                  <p className="text-muted mb-0">Total Students</p>
                </div>
              </div>
            </div>
            <div className="col-md-3">
              <div className="card text-center">
                <div className="card-body">
                  <i className="fas fa-clock fa-2x text-info mb-3"></i>
                  <h3 className="text-info">{dashboardData.total_hours || 0}</h3>
                  <p className="text-muted mb-0">Content Hours</p>
                </div>
              </div>
            </div>
            <div className="col-md-3">
              <div className="card text-center">
                <div className="card-body">
                  <i className="fas fa-star fa-2x text-warning mb-3"></i>
                  <h3 className="text-warning">4.8</h3>
                  <p className="text-muted mb-0">Average Rating</p>
                </div>
              </div>
            </div>
          </>
        ) : (
          <>
            <div className="col-md-3">
              <div className="card text-center">
                <div className="card-body">
                  <i className="fas fa-book fa-2x text-primary mb-3"></i>
                  <h3 className="text-primary">{dashboardData.total_enrolled || 0}</h3>
                  <p className="text-muted mb-0">Enrolled Courses</p>
                </div>
              </div>
            </div>
            <div className="col-md-3">
              <div className="card text-center">
                <div className="card-body">
                  <i className="fas fa-check-circle fa-2x text-success mb-3"></i>
                  <h3 className="text-success">{dashboardData.completed_courses || 0}</h3>
                  <p className="text-muted mb-0">Completed</p>
                </div>
              </div>
            </div>
            <div className="col-md-3">
              <div className="card text-center">
                <div className="card-body">
                  <i className="fas fa-certificate fa-2x text-warning mb-3"></i>
                  <h3 className="text-warning">{dashboardData.certificates_earned || 0}</h3>
                  <p className="text-muted mb-0">Certificates</p>
                </div>
              </div>
            </div>
            <div className="col-md-3">
              <div className="card text-center">
                <div className="card-body">
                  <i className="fas fa-clock fa-2x text-info mb-3"></i>
                  <h3 className="text-info">{dashboardData.total_hours || 0}</h3>
                  <p className="text-muted mb-0">Learning Hours</p>
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Quick Actions */}
      <div className="row mb-5">
        <div className="col-12">
          <h4 className="mb-3">Quick Actions</h4>
          <div className="d-flex flex-wrap gap-3">
            {user?.role === 'teacher' ? (
              <>
                <Link to="/teacher/create-course" className="btn btn-primary">
                  <i className="fas fa-plus me-2"></i>Create New Course
                </Link>
                <Link to="/teacher/courses" className="btn btn-outline-primary">
                  <i className="fas fa-chalkboard me-2"></i>Manage Courses
                </Link>
                <Link to="/teacher/analytics" className="btn btn-outline-secondary">
                  <i className="fas fa-chart-bar me-2"></i>View Analytics
                </Link>
              </>
            ) : (
              <>
                <Link to="/courses" className="btn btn-primary">
                  <i className="fas fa-search me-2"></i>Browse Courses
                </Link>
                <Link to="/courses?enrolled=true" className="btn btn-outline-primary">
                  <i className="fas fa-book me-2"></i>My Courses
                </Link>
                <Link to="/certificates" className="btn btn-outline-secondary">
                  <i className="fas fa-certificate me-2"></i>My Certificates
                </Link>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="row">
        <div className="col-12">
          <h4 className="mb-3">
            {user?.role === 'teacher' ? 'My Courses' : 'Continue Learning'}
          </h4>
          <div className="row g-4">
            {user?.role === 'teacher' ? (
              dashboardData.courses?.slice(0, 3).map(course => (
                <div key={course.id} className="col-md-4">
                  <div className="card">
                    <img
                      src={getCourseImage(course)}
                      className="card-img-top"
                      alt={course.title}
                      style={{ height: '160px', objectFit: 'cover' }}
                    />
                    <div className="card-body">
                      <h6 className="card-title">{course.title}</h6>
                      <p className="card-text text-muted small">
                        {course.description?.substring(0, 80)}...
                      </p>
                      <div className="d-flex justify-content-between align-items-center">
                        <span className="text-primary fw-bold">{formatPrice(course.price)}</span>
                        <Link to={`/teacher/courses/${course.id}`} className="btn btn-sm btn-outline-primary">
                          Manage
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              dashboardData.enrollments?.slice(0, 3).map(enrollment => (
                <div key={enrollment.id} className="col-md-4">
                  <div className="card">
                    <img
                      src={getCourseImage(enrollment.course)}
                      className="card-img-top"
                      alt={enrollment.course?.title}
                      style={{ height: '160px', objectFit: 'cover' }}
                    />
                    <div className="card-body">
                      <h6 className="card-title">{enrollment.course?.title}</h6>
                      <div className="progress mb-2" style={{ height: '6px' }}>
                        <div 
                          className="progress-bar" 
                          style={{ width: `${enrollment.progress}%` }}
                        ></div>
                      </div>
                      <p className="small text-muted mb-2">{enrollment.progress}% Complete</p>
                      <Link to={`/courses/${enrollment.course?.id}`} className="btn btn-sm btn-primary">
                        Continue Learning
                      </Link>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
          
          {((user?.role === 'teacher' && dashboardData.courses?.length === 0) || 
            (user?.role === 'student' && dashboardData.enrollments?.length === 0)) && (
            <div className="text-center py-5">
              <i className={`fas ${user?.role === 'teacher' ? 'fa-chalkboard' : 'fa-book'} fa-3x text-muted mb-3`}></i>
              <h5>
                {user?.role === 'teacher' ? 'No courses created yet' : 'No courses enrolled yet'}
              </h5>
              <p className="text-muted">
                {user?.role === 'teacher' 
                  ? 'Start by creating your first course to share your knowledge with students.'
                  : 'Browse our course catalog and start your learning journey today!'
                }
              </p>
              <Link 
                to={user?.role === 'teacher' ? '/teacher/create-course' : '/courses'} 
                className="btn btn-primary"
              >
                {user?.role === 'teacher' ? 'Create Course' : 'Browse Courses'}
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
