import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { homeAPI } from '../services/api';

const Home = () => {
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [homeData, setHomeData] = useState({
    featured_courses: [],
    categories: [],
    total_courses: 0,
    total_students: 0,
    total_teachers: 0,
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadHomeData();
  }, []);

  const loadHomeData = async () => {
    try {
      const response = await homeAPI.getHomeData();
      setHomeData(response.data);
    } catch (error) {
      console.error('Failed to load home data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/courses?search=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const formatPrice = (price) => {
    return price === 0 ? 'Free' : `$${price}`;
  };

  const getUserAvatar = (user, size = 36) => {
    if (!user) return null;
    
    const initials = user.first_name && user.last_name 
      ? `${user.first_name[0]}${user.last_name[0]}`.toUpperCase()
      : user.username[0].toUpperCase();
    
    return (
      <div 
        className="user-avatar d-flex align-items-center justify-content-center text-white rounded-circle"
        style={{ width: size, height: size, fontSize: size * 0.4 }}
      >
        {initials}
      </div>
    );
  };

  const truncateWords = (text, wordLimit) => {
    if (!text) return '';
    const words = text.split(' ');
    if (words.length <= wordLimit) return text;
    return words.slice(0, wordLimit).join(' ') + '...';
  };

  const getCourseImage = (course) => {
    if (course.banner) {
      return course.banner;
    }

    // Default placeholder for courses without images
    const defaultCourseImage = '/api/placeholder/400/240';

    return defaultCourseImage;
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '50vh' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Hero Section */}
      <section className="hero">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-6">
              <h1>Learn Without Limits</h1>
              <p className="lead mb-4">
                Discover, learn, and grow with Pathshala. Join thousands of students and teachers in our interactive learning platform.
              </p>
              
              {/* Hero Search Form */}
              <form className="search-form mb-4" onSubmit={handleSearch}>
                <div className="input-group">
                  <input 
                    type="text" 
                    className="form-control form-control-lg" 
                    placeholder="What do you want to learn today?"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <button className="btn btn-primary" type="submit">Search</button>
                </div>
              </form>
              
              <div className="d-flex flex-wrap">
                <Link to="/courses" className="btn btn-light btn-lg me-3 mb-3">Explore Courses</Link>
                {!isAuthenticated && (
                  <Link to="/signup?role=teacher" className="btn btn-outline-light btn-lg mb-3">
                    Become a Teacher
                  </Link>
                )}
              </div>
            </div>
            <div className="col-lg-6">
              <div className="bg-light rounded shadow-lg d-flex align-items-center justify-content-center" style={{ height: '400px' }}>
                <i className="fas fa-graduation-cap text-primary" style={{ fontSize: '6rem' }}></i>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Categories */}
      <section className="py-5">
        <div className="container">
          <h2 className="text-center mb-4">Browse Categories</h2>
          <div className="d-flex flex-wrap justify-content-center mb-4">
            <Link to="/courses" className="category-pill bg-light active text-decoration-none">All</Link>
            {homeData.categories.map(category => (
              <Link 
                key={category.id}
                to={`/courses?category=${category.id}`} 
                className="category-pill bg-light text-decoration-none"
              >
                {category.title}
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Courses */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="d-flex justify-content-between align-items-center mb-4">
            <h2>Featured Courses</h2>
            <Link to="/courses" className="btn btn-outline-primary">View All Courses</Link>
          </div>
          <div className="row g-4">
            {homeData.featured_courses.slice(0, 6).map(course => (
              <div key={course.id} className="col-md-6 col-lg-4">
                <div className="card course-card h-100">
                  <img
                    src={getCourseImage(course)}
                    className="card-img-top"
                    alt={course.title}
                  />
                  <div className="card-body">
                    <div className="d-flex justify-content-between align-items-center mb-2">
                      <span className="badge badge-category">{course.category?.title}</span>
                      <div className="rating">
                        {[1,2,3,4,5].map(star => (
                          <i key={star} className="fas fa-star"></i>
                        ))}
                        <span className="ms-1 text-dark">5.0</span>
                      </div>
                    </div>
                    <h5 className="card-title">
                      <Link to={`/courses/${course.id}`} className="text-decoration-none text-dark">
                        {truncateWords(course.title, 8)}
                      </Link>
                    </h5>
                    <p className="card-text">{truncateWords(course.description, 15)}</p>
                    <div className="course-instructor mb-3">
                      {getUserAvatar(course.instructor, 36)}
                      <div className="ms-2">
                        <small className="text-muted">Instructor</small>
                        <p className="mb-0 fw-medium">
                          {course.instructor?.first_name && course.instructor?.last_name 
                            ? `${course.instructor.first_name} ${course.instructor.last_name}`
                            : course.instructor?.username}
                        </p>
                      </div>
                    </div>
                    <div className="d-flex justify-content-between align-items-center">
                      <span className="fw-bold text-primary">{formatPrice(course.price)}</span>
                      <Link to={`/courses/${course.id}`} className="btn btn-sm btn-primary">
                        View Course
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-5">
        <div className="container">
          <h2 className="text-center mb-5">How Pathshala Works</h2>
          <div className="row g-4">
            {/* Admin Role */}
            <div className="col-md-4">
              <div className="card role-card">
                <div className="role-icon">
                  <i className="fas fa-user-shield"></i>
                </div>
                <h3>For Admins</h3>
                <ul className="text-start">
                  <li>Manage users and assign roles</li>
                  <li>Create and organize course categories</li>
                  <li>Access platform-wide statistics</li>
                  <li>Moderate content and maintain quality</li>
                </ul>
                {user?.role === 'admin' ? (
                  <Link to="/dashboard" className="btn btn-outline-primary mt-3">Admin Dashboard</Link>
                ) : (
                  <a href="#" className="btn btn-outline-primary mt-3">Admin Portal</a>
                )}
              </div>
            </div>
            
            {/* Teacher Role */}
            <div className="col-md-4">
              <div className="card role-card">
                <div className="role-icon">
                  <i className="fas fa-chalkboard-teacher"></i>
                </div>
                <h3>For Teachers</h3>
                <ul className="text-start">
                  <li>Create and manage your courses</li>
                  <li>Upload lessons and learning materials</li>
                  <li>Track student progress</li>
                  <li>Interact with your students</li>
                </ul>
                {user?.role === 'teacher' ? (
                  <Link to="/teacher/courses" className="btn btn-outline-primary mt-3">My Courses</Link>
                ) : (
                  <Link to="/signup?role=teacher" className="btn btn-outline-primary mt-3">Become a Teacher</Link>
                )}
              </div>
            </div>
            
            {/* Student Role */}
            <div className="col-md-4">
              <div className="card role-card">
                <div className="role-icon">
                  <i className="fas fa-user-graduate"></i>
                </div>
                <h3>For Students</h3>
                <ul className="text-start">
                  <li>Discover and enroll in courses</li>
                  <li>Learn at your own pace</li>
                  <li>Track your learning progress</li>
                  <li>Earn certificates of completion</li>
                </ul>
                {user?.role === 'student' ? (
                  <Link to="/dashboard" className="btn btn-outline-primary mt-3">My Dashboard</Link>
                ) : (
                  <Link to="/signup?role=student" className="btn btn-outline-primary mt-3">Start Learning</Link>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Statistics */}
      <section className="stats-section">
        <div className="container">
          <h2 className="text-center mb-5">Pathshala by the Numbers</h2>
          <div className="row text-center">
            <div className="col-md-3 col-6 mb-4">
              <div className="stat-value">{homeData.total_courses || "500+"}</div>
              <div className="stat-label">Courses</div>
            </div>
            <div className="col-md-3 col-6 mb-4">
              <div className="stat-value">{homeData.total_teachers || "150+"}</div>
              <div className="stat-label">Expert Teachers</div>
            </div>
            <div className="col-md-3 col-6 mb-4">
              <div className="stat-value">{homeData.total_students || "25,000+"}</div>
              <div className="stat-label">Students</div>
            </div>
            <div className="col-md-3 col-6 mb-4">
              <div className="stat-value">98%</div>
              <div className="stat-label">Satisfaction Rate</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-5 bg-primary text-white">
        <div className="container text-center">
          <h2 className="mb-4">Ready to Start Your Learning Journey?</h2>
          <p className="lead mb-4">Join thousands of students and teachers on Pathshala today.</p>
          <div className="d-flex flex-wrap justify-content-center">
            <Link to="/courses" className="btn btn-light btn-lg me-3 mb-3">Browse Courses</Link>
            {!isAuthenticated && (
              <Link to="/signup" className="btn btn-outline-light btn-lg mb-3">Sign Up for Free</Link>
            )}
          </div>
        </div>
      </section>
    </>
  );
};

export default Home;
