import axios from 'axios';

// Create axios instance with default config
const api = axios.create({
  baseURL: 'http://127.0.0.1:8000/api',
  withCredentials: true, // Important for session-based auth
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to handle CSRF token
api.interceptors.request.use(
  (config) => {
    // Get CSRF token from cookie
    const csrfToken = getCookie('csrftoken');
    if (csrfToken) {
      config.headers['X-CSRFToken'] = csrfToken;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Helper function to get cookie value
function getCookie(name) {
  let cookieValue = null;
  if (document.cookie && document.cookie !== '') {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.substring(0, name.length + 1) === (name + '=')) {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
        break;
      }
    }
  }
  return cookieValue;
}

// Auth API calls
export const authAPI = {
  login: (credentials) => api.post('/auth/login/', credentials),
  register: (userData) => api.post('/auth/register/', userData),
  logout: () => api.post('/auth/logout/'),
  getCurrentUser: () => api.get('/auth/current-user/'),
  getDashboard: () => api.get('/auth/dashboard/'),
};

// Home API calls
export const homeAPI = {
  getHomeData: () => api.get('/auth/home/'),
};

// Course API calls
export const courseAPI = {
  getCourses: (params) => api.get('/courses/', { params }),
  getCourseDetail: (courseId) => api.get(`/courses/${courseId}/`),
  enrollCourse: (courseId) => api.post(`/courses/${courseId}/enroll/`),
  getLessonDetail: (courseId, lessonId) => api.get(`/courses/${courseId}/lessons/${lessonId}/`),
  markLessonComplete: (data) => api.post('/courses/lessons/complete/', data),
};

export default api;
