from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('admin/', admin.site.urls),
    # Original template-based URLs (keep for backward compatibility)
    path('', include('users.urls')),
    path('courses/', include('core.urls')),
    # New API URLs for React frontend
    path('api/auth/', include('users.simple_api_urls')),
    path('api/courses/', include('core.simple_api_urls')),
]

# Serve media files during development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)