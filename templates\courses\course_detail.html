{% extends 'base.html' %}
{% load course_extras %}

{% block title %}{{ course.title }} - Pathshala{% endblock %}

{% block content %}
<!-- Page Header -->
<header class="page-header">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <span class="badge badge-category">{{ course.category_id.title }}</span>
                <h1 class="mt-2 mb-3">{{ course.title }}</h1>
                <p class="lead">{{ course.description }}</p>
                <div class="d-flex flex-wrap align-items-center mt-4">
                    <div class="rating me-3">
                        {% for i in "12345"|make_list %}
                        <i class="fas fa-star"></i>
                        {% endfor %}
                        <span class="text-white ms-1">{{ course|course_rating }}</span>
                    </div>
                    <span class="me-3">({{ total_enrollments }} students enrolled)</span>
                    <span>Last updated: {{ course.updated_at|date:"M d, Y" }}</span>
                </div>
                <div class="mt-3">
                    <span class="d-flex align-items-center text-white">
                        {% user_avatar course.instructor_id 40 %}
                        <span class="ms-2">Created by <strong>{{ course.instructor_id.get_full_name|default:course.instructor_id.username }}</strong></span>
                    </span>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- Course Details -->
<div class="container course-details-container">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <div class="card course-main-content mb-4">
                <!-- Course Tabs -->
                <ul class="nav nav-tabs" id="courseTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="overview-tab" data-bs-toggle="tab"
                            data-bs-target="#overview" type="button" role="tab">Overview</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="curriculum-tab" data-bs-toggle="tab" data-bs-target="#curriculum"
                            type="button" role="tab">Curriculum</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="instructor-tab" data-bs-toggle="tab" data-bs-target="#instructor"
                            type="button" role="tab">Instructor</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="materials-tab" data-bs-toggle="tab" data-bs-target="#materials"
                            type="button" role="tab">Materials</button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content p-4" id="courseTabContent">
                    <!-- Overview Tab -->
                    <div class="tab-pane fade show active" id="overview" role="tabpanel">
                        <h3>Course Description</h3>
                        <p>{{ course.description }}</p>

                        <div class="mb-4">
                            <h4>What you'll learn</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li class="mb-2"><i
                                                class="fas fa-check-circle text-success me-2"></i>Comprehensive
                                            understanding of the subject</li>
                                        <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>Practical
                                            skills and hands-on experience</li>
                                        <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>Real-world
                                            applications and projects</li>
                                        <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>Industry
                                            best practices and standards</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li class="mb-2"><i
                                                class="fas fa-check-circle text-success me-2"></i>Problem-solving
                                            techniques</li>
                                        <li class="mb-2"><i
                                                class="fas fa-check-circle text-success me-2"></i>Professional
                                            development skills</li>
                                        <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>Career
                                            advancement opportunities</li>
                                        <li class="mb-2"><i
                                                class="fas fa-check-circle text-success me-2"></i>Certificate of
                                            completion</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h4>This course includes</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="course-info">
                                        <i class="fas fa-video"></i>
                                        <span>{{ course.duration|format_duration }} of content</span>
                                    </div>
                                    <div class="course-info">
                                        <i class="fas fa-file"></i>
                                        <span>{{ materials.count }} downloadable resources</span>
                                    </div>
                                    <div class="course-info">
                                        <i class="fas fa-play-circle"></i>
                                        <span>{{ lessons.count }} video lessons</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="course-info">
                                        <i class="fas fa-infinity"></i>
                                        <span>Full lifetime access</span>
                                    </div>
                                    <div class="course-info">
                                        <i class="fas fa-mobile-alt"></i>
                                        <span>Access on mobile and desktop</span>
                                    </div>
                                    <div class="course-info">
                                        <i class="fas fa-certificate"></i>
                                        <span>Certificate of completion</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Curriculum Tab -->
                    <!-- Curriculum Tab -->
<div class="tab-pane fade" id="curriculum" role="tabpanel">
    <h3 class="mb-4">Course Curriculum</h3>

    {% if lessons %}
    <div class="curriculum-info mb-4">
        <div class="row">
            <div class="col-md-6">
                <div class="curriculum-stat">
                    <i class="fas fa-play-circle text-primary me-2"></i>
                    <span>{{ lessons.count }} lessons</span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="curriculum-stat">
                    <i class="fas fa-clock text-info me-2"></i>
                    <span>
                        {% for lesson in lessons %}
                        {{ lesson.duration_minutes|add:0 }}
                        {% endfor %} total minutes
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="lessons-list">
        {% for lesson in lessons %}
        <div class="lesson-item-card mb-3">
            <div class="lesson-header d-flex align-items-center justify-content-between">
                <div class="lesson-info d-flex align-items-center">
                    <div class="lesson-icon me-3">
                        {% if lesson.lesson_type == 'youtube' %}
                        <i class="fab fa-youtube text-danger"></i>
                        {% elif lesson.lesson_type == 'upload' %}
                        <i class="fas fa-play-circle text-primary"></i>
                        {% else %}
                        <i class="fas fa-file-alt text-info"></i>
                        {% endif %}
                    </div>
                    <div>
                        <h6 class="lesson-title mb-0">{{ lesson.title }}</h6>
                        <small class="text-muted">{{ lesson.duration_minutes }} minutes</small>
                    </div>
                </div>

                <div class="lesson-actions">
                    {% if is_enrolled %}
                    <a href="{% url 'lesson_detail' course.id lesson.id %}"
                        class="btn btn-primary btn-sm">
                        <i class="fas fa-play me-1"></i> Watch
                    </a>
                    {% else %}
                    <button class="btn btn-outline-secondary btn-sm" disabled>
                        <i class="fas fa-lock me-1"></i> Enroll to Access
                    </button>
                    {% endif %}
                </div>
            </div>

            <div class="lesson-description mt-2">
                <p class="mb-0 text-muted">{{ lesson.description|truncate_words:20 }}</p>
            </div>

            {% if lesson.lesson_type == 'youtube' and lesson.get_youtube_thumbnail and not is_enrolled %}
            <div class="lesson-preview mt-3">
                <img src="{{ lesson.get_youtube_thumbnail }}" alt="Video thumbnail"
                    class="lesson-thumbnail">
                <div class="preview-overlay">
                    <i class="fas fa-play-circle fa-3x text-white"></i>
                </div>
            </div>
            {% endif %}
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-video fa-3x text-muted mb-3"></i>
        <h5>No lessons available yet</h5>
        <p class="text-muted">The instructor is preparing the course content.</p>
    </div>
    {% endif %}
</div>

                    <!-- Instructor Tab -->
                    <div class="tab-pane fade" id="instructor" role="tabpanel">
                        <h3 class="mb-4">Meet Your Instructor</h3>

                        <div class="instructor-profile">
                            {% user_avatar course.instructor_id 80 %}
                            <div class="ms-3">
                                <h4 class="mb-1">{{ course.instructor_id.get_full_name|default:course.instructor_id.username }}</h4>
                                <p class="text-muted mb-2">{{ course.instructor_id.role|title }}</p>
                                <div class="rating mb-2">
                                    {% for i in "12345"|make_list %}
                                    <i class="fas fa-star"></i>
                                    {% endfor %}
                                    <span class="ms-1">4.8 Instructor Rating</span>
                                </div>
                                <div class="mb-2">
                                    <span class="me-3"><i class="fas fa-user-graduate me-1"></i> {{ total_enrollments }}
                                        Students</span>
                                    <span><i class="fas fa-calendar me-1"></i> Joined {{ course.instructor_id.date_joined|date:"M Y" }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <p>{{ course.instructor_id.get_full_name|default:course.instructor_id.username }} is an
                                experienced educator passionate about sharing knowledge and helping students achieve
                                their goals.</p>
                        </div>
                    </div>

                    <!-- Materials Tab -->
                    <div class="tab-pane fade" id="materials" role="tabpanel">
                        <h3 class="mb-4">Course Materials</h3>

                        {% if materials %}
                        <div class="row">
                            {% for material in materials %}
                            <div class="col-md-6 mb-3">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center">
                                            <div class="file-icon me-3">
                                                {% if material.file_type == 'pdf' %}
                                                <i class="fas fa-file-pdf text-danger"></i>
                                                {% elif material.file_type == 'video' %}
                                                <i class="fas fa-file-video text-primary"></i>
                                                {% elif material.file_type == 'document' %}
                                                <i class="fas fa-file-alt text-info"></i>
                                                {% else %}
                                                <i class="fas fa-file text-muted"></i>
                                                {% endif %}
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">{{ material.title }}</h6>
                                                <p class="mb-0 text-muted small">{{material.description|truncate_words:10 }}</p>
                                            </div>
                                            {% if is_enrolled %}
                                            <a href="{{ material.file.url }}" class="btn btn-outline-primary btn-sm"
                                                target="_blank">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <h5>No materials available</h5>
                            <p class="text-muted">Course materials will be added by the instructor.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="course-sidebar">
                <!-- Course Preview Card -->
                <div class="card course-preview-card mb-4">
                    {% if course.banner %}
                    <img src="{{ course.banner.url }}" class="course-preview-img" alt="{{ course.title }}">
                    {% else %}
                    <div class="course-preview-img bg-light d-flex align-items-center justify-content-center">
                        <i class="fas fa-image text-muted fa-3x"></i>
                    </div>
                    {% endif %}
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 class="fw-bold mb-0">{{ course.price|format_price }}</h4>
                            {% if course.price > 0 %}
                            <span class="text-decoration-line-through text-muted">{{ course.price|floatformat:2|add:20}}</span>
                            {% endif %}
                        </div>

                        {% if user.is_authenticated %}
                        {% if is_enrolled %}
                        <div class="d-grid gap-2 mb-4">
                            <a href="#" class="btn btn-success btn-lg">
                                <i class="fas fa-play me-2"></i> Continue Learning
                            </a>
                            {% if enrollment.progress > 0 %}
                            <div class="progress-info">
                                <div class="d-flex justify-content-between mb-1">
                                    <small>Progress</small>
                                    <small>{{ enrollment.progress }}%</small>
                                </div>
                                {% progress_bar enrollment.progress %}
                            </div>
                            {% endif %}
                        </div>
                        {% else %}
                        <div class="d-grid gap-2 mb-4">
                            <form method="POST" action="{% url 'enroll_course' course.id %}">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-shopping-cart me-2"></i> Enroll Now
                                </button>
                            </form>
                            <button class="btn btn-outline-primary">Add to Wishlist</button>
                        </div>
                        {% endif %}
                        {% else %}
                        <div class="d-grid gap-2 mb-4">
                            <a href="{% url 'login' %}?next={{ request.get_full_path }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i> Login to Enroll
                            </a>
                        </div>
                        {% endif %}

                        <div class="mb-4">
                            <h5 class="mb-3">This course includes:</h5>
                            <div class="course-info">
                                <i class="fas fa-video"></i>
                                <span>{{ course.duration|format_duration }} on-demand video</span>
                            </div>
                            <div class="course-info">
                                <i class="fas fa-file"></i>
                                <span>{{ materials.count }} downloadable resources</span>
                            </div>
                            <div class="course-info">
                                <i class="fas fa-play-circle"></i>
                                <span>{{ lessons.count }} video lessons</span>
                            </div>
                            <div class="course-info">
                                <i class="fas fa-infinity"></i>
                                <span>Full lifetime access</span>
                            </div>
                            <div class="course-info">
                                <i class="fas fa-mobile-alt"></i>
                                <span>Access on mobile and desktop</span>
                            </div>
                            <div class="course-info">
                                <i class="fas fa-certificate"></i>
                                <span>Certificate of completion</span>
                            </div>
                        </div>

                        <div class="d-flex justify-content-center">
                            <button class="btn btn-link text-muted">
                                <i class="fas fa-share-alt me-1"></i> Share
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Related Courses -->
                {% if related_courses %}
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Related Courses</h5>
                    </div>
                    <div class="card-body">
                        {% for related_course in related_courses %}
                        <div class="d-flex mb-3">
                            {% if related_course.banner %}
                            <img src="{{ related_course.banner.url }}" alt="{{ related_course.title }}" class="me-3"
                                style="width: 80px; height: 50px; object-fit: cover; border-radius: 4px;">
                            {% else %}
                            <div class="me-3 bg-light d-flex align-items-center justify-content-center"
                                style="width: 80px; height: 50px; border-radius: 4px;">
                                <i class="fas fa-image text-muted"></i>
                            </div>
                            {% endif %}
                            <div>
                                <h6 class="mb-1">
                                    <a href="{% url 'course_detail' related_course.id %}"
                                        class="text-decoration-none">{{ related_course.title|truncate_words:6 }}</a>
                                </h6>
                                <div class="rating">
                                    {% for i in "12345"|make_list %}
                                    <i class="fas fa-star"></i>
                                    {% endfor %}
                                    <span class="ms-1 text-dark">{{ related_course|course_rating }}</span>
                                </div>
                                <p class="mb-0">{{ related_course.price|format_price }}</p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary) 0%, #8c98f3 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
    }

    .course-details-container {
        margin-top: -2rem;
    }

    .course-main-content {
        border-radius: 0.75rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        border: none;
    }

    .course-sidebar {
        position: sticky;
        top: 90px;
    }

    .course-preview-card {
        border-radius: 0.75rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        border: none;
    }

    .course-preview-img {
        border-radius: 0.75rem 0.75rem 0 0;
        width: 100%;
        height: 200px;
        object-fit: cover;
    }

    .course-info {
        display: flex;
        align-items: center;
        margin-bottom: 0.75rem;
    }

    .course-info i {
        color: var(--primary);
        margin-right: 0.5rem;
        width: 20px;
        text-align: center;
    }

    .instructor-profile {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .nav-tabs .nav-link {
        color: var(--dark);
        font-weight: 500;
        border: none;
        padding: 1rem 1.5rem;
    }

    .nav-tabs .nav-link.active {
        color: var(--primary);
        border-bottom: 2px solid var(--primary);
        background-color: transparent;
    }

    .file-icon {
        font-size: 1.5rem;
    }

    @media (max-width: 991.98px) {
        .course-sidebar {
            position: static;
            margin-top: 2rem;
        }

        .course-details-container {
            margin-top: 0;
        }
    }

    .curriculum-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
    }

    .curriculum-stat {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .lesson-item-card {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        background: white;
        transition: all 0.3s;
    }

    .lesson-item-card:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .lesson-icon {
        font-size: 1.5rem;
        width: 40px;
        text-align: center;
    }

    .lesson-title {
        font-weight: 600;
        color: var(--dark);
    }

    .lesson-thumbnail {
        width: 100%;
        max-width: 300px;
        height: 180px;
        object-fit: cover;
        border-radius: 8px;
        position: relative;
    }

    .lesson-preview {
        position: relative;
        display: inline-block;
    }

    .preview-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 8px;
        cursor: pointer;
        opacity: 0;
        transition: opacity 0.3s;
    }

    .lesson-preview:hover .preview-overlay {
        opacity: 1;
    }
</style>
{% endblock %}