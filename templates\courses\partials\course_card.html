{% load course_extras %}
<div class="card course-card h-100">
    {% if course.banner %}
        <img src="{{ course.banner.url }}" class="card-img-top" alt="{{ course.title }}">
    {% else %}
        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 240px;">
            <i class="fas fa-image text-muted fa-3x"></i>
        </div>
    {% endif %}
    
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <span class="badge badge-category">{{ course.category_id.title }}</span>
            <div class="rating">
                {% for i in "12345"|make_list %}
                    <i class="fas fa-star"></i>
                {% endfor %}
                <span class="ms-1 text-dark">{{ course|course_rating }}</span>
            </div>
        </div>
        
        <h5 class="card-title">
            <a href="{% url 'course_detail' course.id %}" class="text-decoration-none text-dark">
                {{ course.title|truncate_words:8 }}
            </a>
        </h5>
        
        <p class="card-text">{{ course.description|truncate_words:15 }}</p>
        
        <div class="course-instructor mb-3">
            {% user_avatar course.instructor_id 36 %}
            <div class="ms-2">
                <small class="text-muted">Instructor</small>
                <p class="mb-0 fw-medium">{{ course.instructor_id.get_full_name|default:course.instructor_id.username }}</p>
            </div>
        </div>
        
        {% if is_enrolled %}
            <div class="mb-3">
                {% progress_bar progress %}
            </div>
        {% endif %}
        
        <div class="d-flex justify-content-between align-items-center">
            <span class="fw-bold text-primary">{{ course.price|format_price }}</span>
            <a href="{% url 'course_detail' course.id %}" class="btn btn-sm btn-primary">
                {% if is_enrolled %}
                    {% if progress == 100 %}
                        Review Course
                    {% else %}
                        Continue Learning
                    {% endif %}
                {% else %}
                    View Course
                {% endif %}
            </a>
        </div>
    </div>
    
    {% if is_enrolled %}
        <div class="card-footer bg-white">
            <small class="text-muted">
                {% if progress == 100 %}
                    Completed
                {% else %}
                    {{ progress }}% Complete
                {% endif %}
            </small>
        </div>
    {% endif %}
</div>