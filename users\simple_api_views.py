import json
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth import login, logout, authenticate
from django.contrib.auth.decorators import login_required
from .models import User
from core.models import Course, Category, Enrollment


@csrf_exempt
@require_http_methods(["POST"])
def register_api(request):
    """Simple API endpoint for user registration"""
    try:
        data = json.loads(request.body)
        
        first_name = data.get('first_name')
        last_name = data.get('last_name')
        email = data.get('email')
        username = data.get('username')
        password = data.get('password')
        password_confirm = data.get('password_confirm')
        role = data.get('role', 'student')
        mobile_no = data.get('mobile_no', '')

        # Validation
        if password != password_confirm:
            return JsonResponse({'error': 'Passwords do not match!'}, status=400)
        
        if User.objects.filter(username=username).exists():
            return JsonResponse({'error': 'Username already exists!'}, status=400)
        
        if User.objects.filter(email=email).exists():
            return JsonResponse({'error': 'Email already exists!'}, status=400)
        
        # Create user
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password,
            first_name=first_name,
            last_name=last_name,
            role=role
        )
        if mobile_no:
            user.mobile_no = mobile_no
            user.save()
        
        login(request, user)
        
        return JsonResponse({
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': user.role,
            },
            'message': 'Registration successful!'
        })
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def login_api(request):
    """Simple API endpoint for user login"""
    try:
        data = json.loads(request.body)
        username = data.get('username')
        password = data.get('password')

        user = authenticate(request, username=username, password=password)

        if user is not None:
            login(request, user)
            return JsonResponse({
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'role': user.role,
                },
                'message': 'Login successful!'
            })
        else:
            return JsonResponse({'error': 'Invalid username or password'}, status=400)
            
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def logout_api(request):
    """Simple API endpoint for user logout"""
    logout(request)
    return JsonResponse({'message': 'Logout successful!'})


@require_http_methods(["GET"])
def current_user_api(request):
    """Simple API endpoint to get current user info"""
    if request.user.is_authenticated:
        return JsonResponse({
            'user': {
                'id': request.user.id,
                'username': request.user.username,
                'email': request.user.email,
                'first_name': request.user.first_name,
                'last_name': request.user.last_name,
                'role': request.user.role,
            },
            'is_authenticated': True
        })
    return JsonResponse({
        'user': None,
        'is_authenticated': False
    })


@require_http_methods(["GET"])
def home_api(request):
    """Simple API endpoint for home page data"""
    featured_courses = Course.objects.filter(is_active=True)
    categories = Category.objects.filter(is_active=True)
    
    # Convert courses to dict
    courses_data = []
    for course in featured_courses:
        courses_data.append({
            'id': course.id,
            'title': course.title,
            'description': course.description,
            'price': course.price,
            'duration': course.duration,
            'banner': course.banner.url if course.banner and hasattr(course.banner, 'url') else None,
            'category': {
                'id': course.category_id.id,
                'title': course.category_id.title,
            } if course.category_id else None,
            'instructor': {
                'id': course.instructor_id.id,
                'username': course.instructor_id.username,
                'first_name': course.instructor_id.first_name,
                'last_name': course.instructor_id.last_name,
            } if course.instructor_id else None,
        })
    
    # Convert categories to dict
    categories_data = []
    for category in categories:
        categories_data.append({
            'id': category.id,
            'title': category.title,
        })
    
    return JsonResponse({
        'featured_courses': courses_data,
        'categories': categories_data,
        'total_courses': featured_courses.count(),
        'total_students': User.objects.filter(role='student').count(),
        'total_teachers': User.objects.filter(role='teacher').count(),
    })


@login_required
@require_http_methods(["GET"])
def dashboard_api(request):
    """Simple API endpoint for user dashboard data"""
    user = request.user
    
    if user.role == 'teacher':
        teacher_courses = Course.objects.filter(instructor_id=user)
        total_students = Enrollment.objects.filter(course_id__instructor_id=user).count()
        
        # Convert courses to dict
        courses_data = []
        for course in teacher_courses:
            courses_data.append({
                'id': course.id,
                'title': course.title,
                'description': course.description,
                'price': course.price,
                'duration': course.duration,
                'banner': course.banner.url if course.banner and hasattr(course.banner, 'url') else None,
            })
        
        return JsonResponse({
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': user.role,
            },
            'courses': courses_data,
            'total_courses': teacher_courses.count(),
            'total_students': total_students,
            'total_hours': sum([course.duration for course in teacher_courses])
        })
    else:  # student
        enrollments = Enrollment.objects.filter(student_id=user)
        completed_courses = enrollments.filter(is_completed=True).count()
        
        # Convert enrollments to dict
        enrollments_data = []
        for enrollment in enrollments:
            enrollments_data.append({
                'id': enrollment.id,
                'progress': enrollment.progress,
                'is_completed': enrollment.is_completed,
                'course': {
                    'id': enrollment.course_id.id,
                    'title': enrollment.course_id.title,
                    'description': enrollment.course_id.description,
                    'banner': enrollment.course_id.banner.url if enrollment.course_id.banner and hasattr(enrollment.course_id.banner, 'url') else None,
                }
            })
        
        return JsonResponse({
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': user.role,
            },
            'enrollments': enrollments_data,
            'total_enrolled': enrollments.count(),
            'completed_courses': completed_courses,
            'certificates_earned': enrollments.filter(is_certificate_ready=True).count(),
            'total_hours': sum([e.course_id.duration for e in enrollments if e.is_completed])
        })
